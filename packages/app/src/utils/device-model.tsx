import { DeviceModel } from "@/api/model/device";

export const MODEL_LABELS: Record<DeviceModel, string> = {
  LIC: "Controlador de Irrigação Localizado",
  "WPC-PL10": "Controlador de Bomba d'Água - PL10",
  "WPC-PL50": "Controlador de Bomba d'Água - PL50",
  VC: "Controlador de Válvula",
  RM: "Monitor de Reservatório",
} as const;

export const MODEL_ABBREVIATIONS: Record<DeviceModel, string> = {
  LIC: "CI",
  "WPC-PL10": "CB-PL10",
  "WPC-PL50": "CB-PL50",
  VC: "CV",
  RM: "MN",
} as const;
export function getDeviceModelLabel(model: DeviceModel): string {
  return MODEL_LABELS[model] || "Modelo Desconhecido";
}
export function getDeviceModelAbbreviation(model: DeviceModel): string {
  return MODEL_ABBREVIATIONS[model] || "MD";
}
export function getDeviceModelIcon(model: DeviceModel): React.ReactNode {
  switch (model) {
    case "LIC":
      return <span className="text-green-600">📡</span>;
    case "WPC-PL10":
    case "WPC-PL50":
      return <span className="text-blue-600">💧</span>;
    case "VC":
      return <span className="text-yellow-600">🌱</span>;
    case "RM":
      return <span className="text-red-600">🏗️</span>;
    default:
      return <span className="text-gray-600">❓</span>;
  }
}

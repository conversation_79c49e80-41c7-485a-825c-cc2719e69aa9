import LogoSymbol from "@/assets/logo-symbol-transparent-79x96.png"; // Adjust the import path as needed
import LogoWordmark from "@/assets/logo-wordmark-256x48.png"; // Adjust the import path as needed
import { selectedAccountUserIdAtom, selectedPropertyIdAtom } from "@/store";
import { propertiesAtom } from "@/store/data";
import { useAtomValue, useSetAtom } from "jotai";
import { ArrowLeft, ChevronRight, Loader2, MapPin, Plus } from "lucide-react";
import React from "react";
import { useLocation } from "wouter";
import Button from "@/components/ui/Button";

function PropertySelectionPage() {
  const [, setLocation] = useLocation();
  const getSelectedAccountUserId = useSetAtom(selectedAccountUserIdAtom);
  const setSelectedPropertyId = useSetAtom(selectedPropertyIdAtom);
  const properties = useAtomValue(propertiesAtom);

  React.useEffect(() => {
    const selectedAccountUserId = getSelectedAccountUserId();
    // Redirect if no account selected
    if (!selectedAccountUserId) {
      console.warn(
        "PropertySelectionPage - No account selected, redirecting to account selection"
      );
      setLocation("/select-account");
      return;
    }
    // If properties are loaded and empty, redirect to no-property page
    if (properties && properties.length === 0) {
      console.warn(
        "PropertySelectionPage - No properties found, redirecting to no-property page"
      );
      setLocation("/no-property");
      return;
    }
    // If only one property, auto-select it and go to main app
    if (properties && properties.length === 1) {
      console.log(
        "PropertySelectionPage - Only one property found, auto-selecting"
      );
      setSelectedPropertyId(properties[0].id);
      setLocation("/app/dashboard");
      return;
    }
  }, [
    getSelectedAccountUserId,
    properties,
    setLocation,
    setSelectedPropertyId,
  ]);

  const handlePropertySelect = (propertyId: string) => {
    setSelectedPropertyId(propertyId);
    // setLocation(`/property/${propertyId}`);
    setLocation(`/app/dashboard`);
  };

  const handleCreateProperty = () => {
    // TODO: Implement property creation flow
    setLocation("/property/create");
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pt-BR");
  };

  const formatAddress = (property: any) => {
    const parts = [
      property.address_street_name,
      property.address_street_number,
      property.address_neighborhood,
      property.address_city,
      property.address_state,
    ].filter((v) => v !== undefined && v !== null && v !== "");
    return parts.join(", ");
  };

  // Loading state: if properties is undefined/null, show loading
  if (!properties) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-green-50">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-green-600 mx-auto mb-4" />
          <p className="text-gray-600">Carregando suas propriedades...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-4 px-4">
      <div className="mx-auto flex flex-col items-center text-center">
        <img
          src={LogoSymbol}
          alt="Description"
          className="mb-4 object-cover h-16"
        />
        <img src={LogoWordmark} alt="Irriga+ Wordmark" className="h-8 mb-2" />
      </div>
      <div className="max-w-5xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          {/* <div className="mx-auto w-16 h-16 bg-green-600 rounded-2xl flex items-center justify-center mb-4">
            <Building2 className="w-8 h-8 text-white" />
          </div> */}
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Selecione uma Propriedade
          </h1>
          <p className="text-gray-600">
            Escolha a propriedade que deseja gerenciar nesta sessão.
          </p>
        </div>

        {/* Properties Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {properties.map((property) => (
            <div
              key={property.id}
              className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-200 cursor-pointer transform hover:-translate-y-1"
              onClick={() => handlePropertySelect(property.id)}
            >
              <div className="p-4">
                {/* Property Info */}
                <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center justify-between">
                  <span>{property.name}</span>
                  <ChevronRight className="w-5 h-5 text-green-600" />
                </h3>

                {/* Property Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex gap-1 ml-2">
                    <span className="text-xs text-green-700 bg-green-100 px-2 py-1 rounded-md">
                      {property.devices?.length || 0} dispositivo(s)
                    </span>
                    <span className="text-xs text-blue-700 bg-blue-100 px-2 py-1 rounded-md">
                      {property.projects?.length || 0} projeto(s)
                    </span>
                    {property.water_pumps &&
                      property.water_pumps.length > 0 && (
                        <span className="text-xs text-purple-700 bg-purple-100 px-2 py-1 rounded-md">
                          {property.water_pumps.length} bomba(s)
                        </span>
                      )}
                  </div>
                </div>

                {/* Address */}
                {formatAddress(property) && (
                  <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                    {formatAddress(property)}
                  </p>
                )}

                {/* Metadata */}
                {property.metadata && (
                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Área:</span>
                      <span className="font-medium text-gray-900">
                        {property.metadata.area_hectares} ha
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Cultura:</span>
                      <span className="font-medium text-gray-900">
                        {property.metadata.crop_type}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Irrigação:</span>
                      <span className="font-medium text-gray-900">
                        {property.metadata.irrigation_type}
                      </span>
                    </div>
                    {property.metadata.environment_type && (
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Tipo:</span>
                        <span className="font-medium text-gray-900">
                          {property.metadata.environment_type}
                        </span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* No Properties State */}
        {properties.length === 0 && (
          <div className="text-center py-12">
            <div className="mx-auto w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mb-4">
              <MapPin className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Nenhuma propriedade encontrada
            </h3>
            <p className="text-gray-600 mb-6">
              Esta conta não possui propriedades cadastradas. Crie uma nova
              propriedade para começar.
            </p>
            <Button
              onClick={handleCreateProperty}
              icon={<Plus className="w-5 h-5" />}
              iconPosition="left"
              size="lg"
            >
              Criar Propriedade
            </Button>
          </div>
        )}

        {/* Create Property Button - Show when there are properties */}
        {properties.length > 0 && (
          <div className="mt-8 text-center">
            <Button
              variant="secondary"
              onClick={handleCreateProperty}
              icon={<Plus className="w-4 h-4" />}
              iconPosition="left"
              size="sm"
            >
              Adicionar Nova Propriedade
            </Button>
          </div>
        )}

        {/* Footer */}
        <div className="mt-12 text-center">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setLocation("/select-account")}
            icon={<ArrowLeft className="w-4 h-4" />}
            iconPosition="left"
          >
            Voltar às contas
          </Button>
        </div>
      </div>
    </div>
  );
}

export default PropertySelectionPage;

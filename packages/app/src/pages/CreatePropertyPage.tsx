import { PropertyWizard, PropertyWizardData } from "@/components";
import {
  createProperty<PERSON>tom,
  currentAccountAtom,
  hasActiveLoadingAtom,
  propertyByIdAtom,
  selectedPropertyIdAtom,
  updatePropertyAtom,
} from "@/store";
import { useAtomValue, useSetAtom } from "jotai";
import React, { useCallback, useEffect, useMemo } from "react";
import { useLocation, useRoute } from "wouter";

function CreatePropertyPage(): React.JSX.Element {
  const [, setLocation] = useLocation();

  // Check which route we're on to determine mode
  const [,] = useRoute("/property/create");
  const [matchEdit, editParams] = useRoute("/property/edit/:id");

  const isEditMode = !!matchEdit;
  const propertyId = editParams?.id;

  // Atoms
  const selectedAccount = useAtomValue(currentAccountAtom);
  const createProperty = useSetAtom(createPropertyAtom);
  const updateProperty = useSetAtom(updatePropertyAtom);
  const getPropertyById = useAtomValue(propertyByIdAtom);
  const setSelectedPropertyId = useSetAtom(selectedPropertyIdAtom);
  const isLoading = useAtomValue(hasActiveLoadingAtom);

  // Get property for editing
  const existingProperty = useMemo(() => {
    if (isEditMode && propertyId) {
      return getPropertyById(propertyId);
    }
    return null;
  }, [isEditMode, propertyId, getPropertyById]);

  // Prepare initial data for the form
  const initialFormData = useMemo(() => {
    if (isEditMode && existingProperty) {
      // Convert GeoJSON Point to PropertyForm Point format
      let formPoint: import("@/components").Point | null = null;
      if (existingProperty.point && existingProperty.point.coordinates) {
        const [lng, lat] = existingProperty.point.coordinates;
        if (typeof lng === "number" && typeof lat === "number") {
          formPoint = {
            type: "Point",
            coordinates: [lng, lat],
          };
        }
      }

      return {
        name: existingProperty.name || "",
        timezone: existingProperty.timezone || "America/Sao_Paulo",
        point: formPoint,
        address_postal_code: existingProperty.address_postal_code,
        address_street_name: existingProperty.address_street_name,
        address_street_number: existingProperty.address_street_number,
        address_complement: existingProperty.address_complement,
        address_neighborhood: existingProperty.address_neighborhood,
        address_city: existingProperty.address_city,
        address_state: existingProperty.address_state,
        address_country: existingProperty.address_country || "Brasil",
        backwash_duration_minutes: existingProperty.backwash_duration_minutes,
        backwash_period_minutes: existingProperty.backwash_period_minutes,
        backwash_delay_seconds: existingProperty.backwash_delay_seconds,
        rain_gauge_enabled: existingProperty.rain_gauge_enabled || false,
        rain_gauge_resolution_mm: existingProperty.rain_gauge_resolution_mm,
        precipitation_volume_limit_mm:
          existingProperty.precipitation_volume_limit_mm,
        precipitation_suspended_duration_hours:
          existingProperty.precipitation_suspended_duration_hours,
      };
    }
    return {};
  }, [isEditMode, existingProperty]);

  // Redirect if editing but property not found
  useEffect(() => {
    if (isEditMode && propertyId && !existingProperty) {
      console.error(`Property with ID ${propertyId} not found`);
      setLocation("/select-property");
    }
  }, [isEditMode, propertyId, existingProperty, setLocation]);

  const handleFormSubmit = useCallback(
    async (formData: PropertyWizardData) => {
      // Convert PropertyForm Point to GeoJSON Point format
      let geoJsonPoint: GeoJSON.Point | null = null;
      if (formData.point) {
        geoJsonPoint = {
          type: "Point",
          coordinates: formData.point.coordinates,
        };
      }

      // Prepare the data according to the API model
      const propertyData = {
        account: formData.account,
        name: formData.name,
        timezone: formData.timezone,
        point: geoJsonPoint,
        address_street_name: formData.address_street_name,
        address_street_number: formData.address_street_number,
        address_complement: formData.address_complement,
        address_neighborhood: formData.address_neighborhood,
        address_city: formData.address_city,
        address_state: formData.address_state,
        address_postal_code: formData.address_postal_code,
        address_country: formData.address_country,
        backwash_duration_minutes: formData.backwash_duration_minutes,
        backwash_period_minutes: formData.backwash_period_minutes,
        backwash_delay_seconds: formData.backwash_delay_seconds,
        rain_gauge_enabled: formData.rain_gauge_enabled,
        rain_gauge_resolution_mm: formData.rain_gauge_resolution_mm,
        precipitation_volume_limit_mm: formData.precipitation_volume_limit_mm,
        precipitation_suspended_duration_hours:
          formData.precipitation_suspended_duration_hours,
      };

      if (isEditMode && propertyId) {
        // Update existing property
        const result = await updateProperty({
          id: propertyId,
          data: propertyData,
        });
        if (result.success) {
          setSelectedPropertyId(propertyId);
          setLocation(`/app/dashboard`);
        }
      } else {
        // Create new property
        const result = await createProperty({
          ...propertyData,
          account: selectedAccount?.id,
        });
        if (result.success && result.data) {
          setSelectedPropertyId(result.data.id);
          setLocation(`/app/dashboard`);
        }
      }
    },
    [isEditMode, propertyId, updateProperty, createProperty, setLocation]
  );

  const handleCancel = useCallback(() => {
    if (isEditMode && propertyId) {
      setLocation(`/property/${propertyId}`);
    } else {
      setLocation("/no-property");
    }
  }, [isEditMode, propertyId, setLocation]);

  const submitButtonText = isEditMode
    ? "Salvar Alterações"
    : "Criar Propriedade";

  return (
    <div className="h-screen bg-gray-50">
      <PropertyWizard
        initialData={initialFormData}
        isEditMode={isEditMode}
        isLoading={isLoading}
        onSubmit={handleFormSubmit}
        onCancel={handleCancel}
        submitButtonText={submitButtonText}
      />
    </div>
  );
}

export default CreatePropertyPage;

import LogoSymbol from "@/assets/logo-symbol-transparent-79x96.png"; // Adjust the import path as needed
import LogoWordmark from "@/assets/logo-wordmark-256x48.png"; // Adjust the import path as needed
import { assignAccountAtom, availableAccountsAtom } from "@/store";
import { useAtomValue, useSetAtom } from "jotai";
import { AlertTriangle, ArrowLeft, ChevronRight } from "lucide-react";
import { useLocation } from "wouter";
import { Button } from "@/components/ui/Button";

function AccountSelectionPage() {
  const accounts = useAtomValue(availableAccountsAtom);
  const [, setLocation] = useLocation();
  const assignAccount = useSetAtom(assignAccountAtom);
  const isLoading = accounts === undefined || accounts.length === 0;

  const handleAccountSelect = async (accountId: string) => {
    try {
      await assignAccount({ accountUserId: accountId, navigate: setLocation });
    } catch (error) {
      console.error("Failed to assign account:", error);
      // Error handling is already done in the atom
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pt-BR");
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-green-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando suas contas...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-4 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          {false && (
            <div className="mx-auto w-16 h-16 bg-green-600 rounded-2xl flex items-center justify-center mb-4">
              <svg
                className="w-8 h-8 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                />
              </svg>
            </div>
          )}
          <div className="mx-auto flex flex-col items-center text-center">
            <img
              src={LogoSymbol}
              alt="Description"
              className="mb-4 object-cover h-16"
            />
            <img
              src={LogoWordmark}
              alt="Irriga+ Wordmark"
              className="h-8 mb-2"
            />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Selecione uma Conta
          </h1>
          <p className="text-gray-600">
            Você tem acesso a múltiplas contas. Escolha qual deseja acessar.
          </p>
        </div>

        {/* Accounts Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {accounts.map((accountUser) => {
            const acc = accountUser.account;
            const owner = acc?.owner;
            const role = accountUser.role;
            return (
              <div
                key={accountUser.id}
                className="bg-white rounded-2xl shadow-lg border border-green-200/60 overflow-hidden hover:shadow-xl transition-shadow duration-200 cursor-pointer group flex items-stretch min-h-[92px]"
                onClick={() => handleAccountSelect(accountUser.id)}
              >
                {/* Content (left) */}
                <div className="flex-1 p-4 flex flex-col justify-center">
                  {/* Email */}
                  <div
                    className="text-base font-bold text-gray-900 mb-1 truncate"
                    title={owner?.email || ""}
                  >
                    {owner?.email || "(sem email)"}
                  </div>
                  {/* Owner */}
                  <div className="text-sm text-gray-700 mb-1">
                    {owner?.first_name || owner?.last_name ? (
                      <>
                        Proprietário: {owner?.first_name} {owner?.last_name}
                      </>
                    ) : (
                      <span className="italic text-gray-400">
                        Sem nome cadastrado
                      </span>
                    )}
                  </div>
                  {/* Access */}
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-sm text-gray-700">Acesso:</span>
                    <span className="text-xs font-semibold text-green-700 bg-green-50 border border-green-200 px-2 py-0.5 rounded-md">
                      {role.charAt(0).toUpperCase() +
                        role.slice(1).toLowerCase()}
                    </span>
                  </div>
                </div>
                {/* Chevron (right) */}
                <div className="flex items-center pr-4">
                  <ChevronRight className="w-6 h-6 text-gray-500 group-hover:text-green-600 group-hover:translate-x-1 transition-transform" />
                </div>
              </div>
            );
          })}
        </div>

        {/* No Accounts State */}
        {accounts.length === 0 && !isLoading && (
          <div className="text-center py-12">
            <div className="mx-auto w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mb-4">
              <AlertTriangle className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Nenhuma conta encontrada
            </h3>
            <p className="text-gray-600 mb-4">
              Você não tem acesso a nenhuma conta no momento.
            </p>
            <Button
              onClick={() => setLocation("/login")}
              variant="primary"
              size="md"
            >
              Voltar ao Login
            </Button>
          </div>
        )}

        {/* Footer */}
        <div className="mt-12 text-center">
          <Button
            onClick={() => setLocation("/login")}
            variant="ghost"
            size="md"
            icon={<ArrowLeft className="w-4 h-4" />}
            iconPosition="left"
          >
            Voltar ao login
          </Button>
        </div>
      </div>
    </div>
  );
}

export default AccountSelectionPage;

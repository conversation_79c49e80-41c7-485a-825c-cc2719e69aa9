import { PropertyOverview } from "@/components/PropertyOverview";
import { appShell, selectedPropertyAtom } from "@/store";
import { useAtomValue, useSetAtom } from "jotai";
import { useEffect } from "react";

function DashboardPage() {
  const setBackButton = useSetAtom(appShell.backButtonAtom);
  useEffect(() => {
    setBackButton(false);
  });
  const selectedProperty = useAtomValue(selectedPropertyAtom);

  if (!selectedProperty) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Propriedade não encontrada
          </h2>
          <p className="text-gray-600">
            A propriedade selecionada não foi encontrada.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold text-gray-900 mb-2 ">
        {selectedProperty.name}
      </h1>

      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-2">
        <PropertyOverview
          showTitle={false}
          showAddress={false}
          propertyId={selectedProperty.id}
        />
      </div>
      {/* You can add more dashboard widgets below as needed */}
    </div>
  );
}

export default DashboardPage;

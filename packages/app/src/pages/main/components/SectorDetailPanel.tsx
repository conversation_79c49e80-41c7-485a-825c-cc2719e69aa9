import { DeviceModel } from "@/api/model/device";
import { ChevronDown } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import ConfirmModal from "../../../components/ConfirmModal";
import { useAtomValue } from "jotai";
import { propertiesAtom } from "@/store";
import { deviceToLicMapAtom } from "@/store/data";
import { MODEL_LABELS } from "@/utils/device-model";
import Button from "@/components/ui/Button";

interface Device {
  id: string;
  name: string;
  model: DeviceModel;
  identifier: string;
}

interface Sector {
  id: string;
  projectId: string;
  name: string;
  description: string;
  controllerId: string;
  controllerOutput: number; // 1, 2, 3, or 4
  area: number;
  power: number;
}

interface SectorDetailPageProps {
  sector?: Sector; // undefined for creating new sector
  projectId: string;
  devices: Device[];
  _sectors?: Sector[]; // All sectors in the project for filtering
  onSave: (sector: Omit<Sector, "id">) => void;
  onDelete?: (sectorId: string) => void;
  onCancel: () => void;
}

function SectorDetailPanel({
  sector,
  projectId,
  devices,
  _sectors = [],
  onSave,
  onDelete,
  onCancel,
}: SectorDetailPageProps) {
  const [saving, setSaving] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [formData, setFormData] = useState({
    name: sector?.name || "",
    description: sector?.description || "",
    controllerId: sector?.controllerId || "",
    controllerOutput: sector?.controllerOutput || null,
    area: sector?.area?.toString() || "",
    power: sector?.power?.toString() || "0",
  });
  const properties = useAtomValue(propertiesAtom);
  const deviceToLicMap = useAtomValue(deviceToLicMapAtom);

  const isEditing = !!sector;

  // Get the LIC for the current project and check if irrigation pump has frequency inverter
  const { projectLicId, hasFrequencyInverter } = useMemo(() => {
    const projectData = properties
      .flatMap((p) => p.projects)
      .find((p) => p?.id === projectId);
    const licControllerId = projectData?.localized_irrigation_controller;
    
    // Check if the project's irrigation pump has frequency inverter
    const irrigationPump = properties
      .flatMap((p) => p.water_pumps)
      .find((pump) => pump.id === projectData?.irrigation_water_pump);
    
    return {
      projectLicId: licControllerId,
      hasFrequencyInverter: irrigationPump?.has_frequency_inverter || false,
    };
  }, [properties, projectId]);

  // Reset form data when sector prop changes or when creating new sector
  useEffect(() => {
    if (sector) {
      // Editing existing sector
      setFormData({
        name: sector.name || "",
        description: sector.description || "",
        controllerId: sector.controllerId || "",
        controllerOutput: sector.controllerOutput || 1,
        area: sector.area?.toString() || "",
        power: sector.power?.toString() || "0",
      });
    } else {
      // Creating new sector - reset form
      setFormData({
        name: "",
        description: "",
        controllerId: "",
        controllerOutput: null,
        area: "",
        power: hasFrequencyInverter ? "50" : "0", // Default to 50% if frequency inverter available
      });
    }
  }, [sector, hasFrequencyInverter]);

  const handleInputChange = (field: string, value: string | number) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSave = async () => {
    if (
      !formData.name.trim() ||
      !formData.controllerId ||
      formData.controllerOutput === null
    ) {
      alert("Por favor, preencha todos os campos obrigatórios.");
      return;
    }

    // Parse area string to number, default to 0 if empty or invalid
    const areaValue =
      formData.area.trim() !== "" ? parseFloat(formData.area) : 0;
    const finalAreaValue = isNaN(areaValue) ? 0 : areaValue;

    // Parse power string to number, ensure it's within 0-100 range
    const powerValue = formData.power.trim() !== "" ? parseInt(formData.power) : 0;
    const finalPowerValue = Math.max(0, Math.min(100, isNaN(powerValue) ? 0 : powerValue));

    setSaving(true);
    try {
      onSave({
        projectId,
        name: formData.name,
        description: formData.description,
        controllerId: formData.controllerId,
        controllerOutput: formData.controllerOutput,
        area: finalAreaValue,
        power: finalPowerValue,
      });
    } catch (error) {
      console.error("Error saving sector:", error);
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!sector || !onDelete) return;
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (!sector || !onDelete) return;

    setDeleting(true);
    try {
      onDelete(sector.id);
    } catch (error) {
      console.error("Error deleting sector:", error);
    } finally {
      setDeleting(false);
    }
  };

  const valveControllerDevices = devices.filter((d) => d.model === "VC");

  // Create a map of used valve controller outputs
  const usedOutputs = new Map<string, Set<number>>();
  const allSectors = useMemo(() => {
    return properties.flatMap(
      (property) =>
        property.projects?.flatMap((project) => project.sectors || []) ?? []
    );
  }, [properties]);
  allSectors.forEach((s) => {
    if (s.id === sector?.id) return; // Exclude current sector when editing
    const controllerId = s.valve_controller; // Now always a string
    if (controllerId) {
      if (!usedOutputs.has(controllerId)) {
        usedOutputs.set(controllerId, new Set());
      }
      usedOutputs.get(controllerId)!.add(s.valve_controller_output);
    }
  });

  // Filter available valve controllers (exclude those with all outputs used)
  const availableValveControllers = valveControllerDevices.filter((device) => {
    // Mesh network check
    if (projectLicId) {
      const deviceLicId = deviceToLicMap.get(device.id);
      if (deviceLicId !== projectLicId) {
        return false;
      }
    }

    const usedOutputsForDevice = usedOutputs.get(device.id);
    if (!usedOutputsForDevice) return true; // No outputs used
    return usedOutputsForDevice.size < 4; // Has available outputs
  });

  // Get disabled outputs for the selected controller
  const getDisabledOutputs = (controllerId: string): Set<number> => {
    return usedOutputs.get(controllerId) || new Set();
  };

  return (
    <>
      {/* Form Content */}
      <div className="space-y-6">
        {/* Sector Name */}
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Nome *{projectLicId ?? "asd"}
          </label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => handleInputChange("name", e.target.value)}
            className="w-full px-4 py-4 border border-neutral-200 rounded-xl text-base focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-all duration-150 min-h-12 bg-white"
            placeholder="Nome do setor"
          />
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Descrição
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => handleInputChange("description", e.target.value)}
            className="w-full px-4 py-4 border border-neutral-200 rounded-xl text-base focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-all duration-150 resize-none bg-white"
            rows={3}
            placeholder="Descrição do setor"
          />
        </div>

        {/* Controller Selection */}
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Controlador *
          </label>
          <div className="relative">
            <select
              value={formData.controllerId}
              onChange={(e) =>
                handleInputChange("controllerId", e.target.value)
              }
              className="w-full px-4 py-4 border border-neutral-200 rounded-xl text-base focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-all duration-150 appearance-none bg-white min-h-12"
            >
              <option value="">Selecione um controlador</option>
              {availableValveControllers.map((device) => (
                <option key={device.id} value={device.id}>
                  {device.identifier} - {MODEL_LABELS[device.model]}
                </option>
              ))}
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-neutral-400 pointer-events-none" />
          </div>
        </div>

        {/* Controller Output */}
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-3">
            Saída do controlador *
          </label>
          <div className="grid grid-cols-4 gap-4">
            {[1, 2, 3, 4].map((output) => {
              const disabledOutputs = getDisabledOutputs(formData.controllerId);
              const isOutputInUse = disabledOutputs.has(output);
              const isDisabled = isOutputInUse || !formData.controllerId;

              return (
                <div key={output} className="flex flex-col items-center ">
                  <label
                    key={output}
                    className={`flex items-center justify-center p-4 border border-neutral-200 rounded-xl transition-all duration-150 ${
                      isDisabled
                        ? "cursor-not-allowed bg-neutral-100 opacity-50"
                        : "cursor-pointer hover:bg-neutral-50"
                    }`}
                  >
                    <input
                      type="radio"
                      name="controllerOutput"
                      value={output}
                      checked={formData.controllerOutput === output}
                      disabled={isDisabled}
                      onChange={(e) =>
                        handleInputChange(
                          "controllerOutput",
                          parseInt(e.target.value)
                        )
                      }
                      className="sr-only"
                    />
                    <div className="flex items-center">
                      <div
                        className={`w-4 h-4 rounded-full border-2 mr-2 flex items-center justify-center transition-all duration-150 ${
                          formData.controllerOutput === output
                            ? "border-green-500 bg-green-500"
                            : isDisabled
                              ? "border-neutral-300 bg-neutral-200"
                              : "border-neutral-300"
                        }`}
                      >
                        {formData.controllerOutput === output && (
                          <div className="w-2 h-2 bg-white rounded-full"></div>
                        )}
                      </div>
                      <span
                        className={`text-sm font-medium ${
                          isDisabled ? "text-neutral-400" : "text-neutral-900"
                        }`}
                      >
                        {output}
                      </span>
                    </div>
                  </label>
                  {isOutputInUse && (
                    <span className="text-xs text-neutral-400">(Em uso)</span>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Area */}
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Área (hectares)
          </label>
          <input
            type="number"
            step="0.1"
            min="0"
            value={formData.area}
            onChange={(e) => handleInputChange("area", e.target.value)}
            className="w-full px-4 py-4 border border-neutral-200 rounded-xl text-base focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-all duration-150 min-h-12 bg-white"
            placeholder="0.0"
          />
        </div>

        {/* Power */}
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Potência (%)
            {!hasFrequencyInverter && (
              <span className="text-xs text-neutral-500 ml-2">
                (Bomba sem inversor de frequência)
              </span>
            )}
          </label>
          <input
            type="number"
            min="0"
            max="100"
            step="1"
            value={formData.power}
            onChange={(e) => handleInputChange("power", e.target.value)}
            disabled={!hasFrequencyInverter}
            className={`w-full px-4 py-4 border border-neutral-200 rounded-xl text-base focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-all duration-150 min-h-12 ${
              hasFrequencyInverter 
                ? "bg-white" 
                : "bg-neutral-100 text-neutral-500 cursor-not-allowed"
            }`}
            placeholder="0"
          />
          {hasFrequencyInverter && (
            <p className="text-xs text-neutral-500 mt-1">
              Ajuste a potência da bomba (0-100%). Disponível apenas para bombas com inversor de frequência.
            </p>
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="pt-6 border-t border-neutral-100">
        <div className="flex gap-4">
          <Button
            onClick={handleSave}
            loading={saving}
            disabled={deleting}
            variant="primary"
            size="md"
            className="flex-1 rounded-xl"
          >
            Salvar
          </Button>

          {isEditing && onDelete && (
            <Button
              onClick={handleDelete}
              loading={deleting}
              disabled={saving}
              variant="destructive"
              size="md"
              className="flex-1 rounded-xl"
            >
              Excluir
            </Button>
          )}

          <Button
            onClick={onCancel}
            disabled={saving || deleting}
            variant="secondary"
            size="md"
            className="flex-1 rounded-xl"
          >
            Cancelar
          </Button>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={confirmDelete}
        title="Excluir Setor"
        message={`Tem certeza que deseja excluir o setor "${sector?.name}"? Esta ação não pode ser desfeita.`}
        confirmText="Excluir"
        cancelText="Cancelar"
        variant="danger"
      />
    </>
  );
}

export default SectorDetailPanel;

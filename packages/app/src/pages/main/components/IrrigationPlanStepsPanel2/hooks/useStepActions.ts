import { IrrigationPlanStepFormData } from "@/pages/main/IrrigationPlanWizardPage";
import { useCallback } from "react";

/**
 * Props for the useStepActions hook.
 */
interface UseStepActionsProps {
  steps: IrrigationPlanStepFormData[];
  onUpdateStep: (
    stepId: string,
    updates: Partial<IrrigationPlanStepFormData>
  ) => void;
  onRemoveStep: (stepId: string) => void;
  onSaveStep: (step: IrrigationPlanStepFormData) => void;
  onConfirmSingleDelete?: (stepId: string, sectorName: string) => void;
}

/**
 * Custom hook for handling all step CRUD operations.
 * @param props - Hook configuration
 * @returns Object containing step action handlers
 */
export const useStepActions = ({
  steps,
  onUpdateStep,
  onRemoveStep,
  onSaveStep,
  onConfirmSingleDelete,
}: UseStepActionsProps) => {
  /**
   * Handles editing a single step by finding it and returning it for editing.
   * @param stepId - ID of the step to edit
   * @returns The step to edit or null if not found
   */
  const handleStepEdit = useCallback(
    (stepId: string): IrrigationPlanStepFormData | null => {
      const step = steps.find((s) => s.id === stepId);
      return step || null;
    },
    [steps]
  );

  /**
   * Handles deleting a single step.
   * @param stepId - ID of the step to delete
   */
  const handleStepDelete = useCallback(
    (stepId: string) => {
      if (onConfirmSingleDelete) {
        const step = steps.find((s) => s.id === stepId);
        const sectorName = step ? step.sectorName : "este setor";
        onConfirmSingleDelete(stepId, sectorName);
      } else {
        onRemoveStep(stepId);
      }
    },
    [onRemoveStep, onConfirmSingleDelete, steps]
  );

  /**
   * Handles saving a step after editing.
   * @param step - The updated step data
   */
  const handleStepSave = useCallback(
    (step: IrrigationPlanStepFormData) => {
      onSaveStep(step);
    },
    [onSaveStep]
  );

  /**
   * Handles bulk editing of multiple steps.
   * @param stepIds - Array of step IDs to update
   * @param updates - Partial step data to apply to all selected steps
   */
  const handleBulkEdit = useCallback(
    (stepIds: string[], updates: Partial<IrrigationPlanStepFormData>) => {
      stepIds.forEach((stepId) => {
        // First update the local state
        onUpdateStep(stepId, updates);

        // Then find the updated step and save it to persist changes
        const step = steps.find((s) => s.id === stepId);
        if (step) {
          const updatedStep = { ...step, ...updates };
          onSaveStep(updatedStep);
        }
      });
    },
    [onUpdateStep, onSaveStep, steps]
  );

  return {
    handleStepEdit,
    handleStepDelete,
    handleStepSave,
    handleBulkEdit,
  };
};

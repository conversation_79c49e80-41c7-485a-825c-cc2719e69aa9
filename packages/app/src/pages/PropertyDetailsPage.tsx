import { selectedPropertyIdAtom } from "@/store";
import { useSet<PERSON>tom } from "jotai";
import { useEffect, useState } from "react";
import { useLocation, useRoute } from "wouter";
import Button from "@/components/ui/Button";

interface Device {
  id: string;
  name: string;
  type: string;
  status: "online" | "offline" | "error";
  lastSeen: string;
  location?: string;
}

interface Property {
  id: string;
  name: string;
  address: string;
  city: string;
  state: string;
  area?: string;
  description?: string;
}

/**
 * @deprecated
 * @returns
 */
function PropertyDetailsPage() {
  const [, setLocation] = useLocation();
  const [match, params] = useRoute("/property/:id");
  const [property, setProperty] = useState<Property | null>(null);
  const [devices, setDevices] = useState<Device[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const setSelectedPropertyId = useSetAtom(selectedPropertyIdAtom);

  useEffect(() => {
    const loadPropertyData = async () => {
      setIsLoading(true);
      try {
        // TODO: Replace with actual API calls
        // Simulate API call to fetch property details
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // Mock property data
        const mockProperty: Property = {
          id: params?.id || "1",
          name: "Fazenda São José",
          address: "Estrada Rural, 123",
          city: "Ribeirão Preto",
          state: "SP",
          area: "25.5",
          description:
            "Propriedade principal com sistema de irrigação por gotejamento",
        };

        // Mock devices data
        const mockDevices: Device[] = [
          {
            id: "device-1",
            name: "Controlador Principal",
            type: "irrigation-controller",
            status: "online",
            lastSeen: "2 minutos atrás",
            location: "Setor A",
          },
          {
            id: "device-2",
            name: "Sensor de Umidade - Campo Norte",
            type: "moisture-sensor",
            status: "online",
            lastSeen: "5 minutos atrás",
            location: "Campo Norte",
          },
          {
            id: "device-3",
            name: "Válvula Setor B",
            type: "irrigation-valve",
            status: "offline",
            lastSeen: "2 horas atrás",
            location: "Setor B",
          },
        ];

        setProperty(mockProperty);
        setDevices(mockDevices);
      } catch (error) {
        console.error("Error loading property data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    if (match) {
      loadPropertyData();
    }
  }, [match, params?.id]);

  const handleEnterApp = () => {
    // Store the selected property ID in the persistent atom
    if (property) {
      setSelectedPropertyId(property.id);
    }
    setLocation("/app/dashboard");
  };

  const handleAddDevice = () => {
    // TODO: Implement device addition flow
    alert("Funcionalidade de adicionar dispositivo será implementada");
  };

  const handleDeviceClick = (device: Device) => {
    // TODO: Navigate to device configuration
    console.log("Device clicked:", device);
  };

  const handleBackToProperties = () => {
    setLocation("/select-property");
  };

  const getStatusColor = (status: Device["status"]) => {
    switch (status) {
      case "online":
        return "text-green-600 bg-green-100";
      case "offline":
        return "text-gray-600 bg-gray-100";
      case "error":
        return "text-red-600 bg-red-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getStatusText = (status: Device["status"]) => {
    switch (status) {
      case "online":
        return "Online";
      case "offline":
        return "Offline";
      case "error":
        return "Erro";
      default:
        return "Desconhecido";
    }
  };

  const getDeviceIcon = (type: string) => {
    switch (type) {
      case "irrigation-controller":
        return (
          <svg
            className="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"
            />
          </svg>
        );
      case "moisture-sensor":
        return (
          <svg
            className="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M13 10V3L4 14h7v7l9-11h-7z"
            />
          </svg>
        );
      case "irrigation-valve":
        return (
          <svg
            className="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"
            />
          </svg>
        );
      default:
        return (
          <svg
            className="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"
            />
          </svg>
        );
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-green-50 flex items-center justify-center">
        <div className="text-center">
          <svg
            className="animate-spin -ml-1 mr-3 h-12 w-12 text-green-600 mx-auto mb-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          <p className="text-gray-600">Carregando propriedade...</p>
        </div>
      </div>
    );
  }

  if (!property) {
    return (
      <div className="min-h-screen bg-green-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Propriedade não encontrada
          </h1>
          <button
            onClick={handleBackToProperties}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
          >
            Voltar às Propriedades
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-green-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBackToProperties}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10 19l-7-7m0 0l7-7m-7 7h18"
                  />
                </svg>
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {property.name}
                </h1>
                <p className="text-gray-600">
                  {property.address}, {property.city} - {property.state}
                </p>
              </div>
            </div>
            <button
              onClick={handleEnterApp}
              className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              Acessar Sistema
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Property Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Informações da Propriedade
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Endereço</p>
                  <p className="text-gray-900">{property.address}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">
                    Cidade/Estado
                  </p>
                  <p className="text-gray-900">
                    {property.city} - {property.state}
                  </p>
                </div>
                {property.area && (
                  <div>
                    <p className="text-sm font-medium text-gray-500">Área</p>
                    <p className="text-gray-900">{property.area} hectares</p>
                  </div>
                )}
                <div>
                  <p className="text-sm font-medium text-gray-500">Status</p>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-green-600 font-medium">Ativo</span>
                  </div>
                </div>
              </div>
              {property.description && (
                <div className="mt-4">
                  <p className="text-sm font-medium text-gray-500">Descrição</p>
                  <p className="text-gray-900 mt-1">{property.description}</p>
                </div>
              )}
            </div>
          </div>

          {/* Quick Stats */}
          <div className="space-y-4">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Resumo
              </h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Total de Dispositivos</span>
                  <span className="text-2xl font-bold text-gray-900">
                    {devices.length}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Online</span>
                  <span className="text-lg font-semibold text-green-600">
                    {devices.filter((d) => d.status === "online").length}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Offline</span>
                  <span className="text-lg font-semibold text-gray-600">
                    {devices.filter((d) => d.status === "offline").length}
                  </span>
                </div>
                {devices.filter((d) => d.status === "error").length > 0 && (
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Com Erro</span>
                    <span className="text-lg font-semibold text-red-600">
                      {devices.filter((d) => d.status === "error").length}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Devices Section */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold text-gray-900">
                  Dispositivos de Irrigação
                </h2>
                <p className="text-gray-600">
                  Gerencie e monitore seus dispositivos
                </p>
              </div>
              <button
                onClick={handleAddDevice}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
                <span>Adicionar Dispositivo</span>
              </button>
            </div>
          </div>

          <div className="p-6">
            {devices.length === 0 ? (
              <div className="text-center py-12">
                <div className="mx-auto w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mb-4">
                  <svg
                    className="w-8 h-8 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Nenhum dispositivo configurado
                </h3>
                <p className="text-gray-600 mb-4">
                  Adicione dispositivos para começar a monitorar sua irrigação
                </p>
                <button
                  onClick={handleAddDevice}
                  className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  Adicionar Primeiro Dispositivo
                </button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {devices.map((device) => (
                  <div
                    key={device.id}
                    onClick={() => handleDeviceClick(device)}
                    className="border border-gray-200 rounded-lg p-4 hover:border-green-300 hover:shadow-md transition-all cursor-pointer"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="p-2 bg-green-100 rounded-lg text-green-600">
                        {getDeviceIcon(device.type)}
                      </div>
                      <span
                        className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
                          device.status
                        )}`}
                      >
                        {getStatusText(device.status)}
                      </span>
                    </div>
                    <h3 className="font-medium text-gray-900 mb-1">
                      {device.name}
                    </h3>
                    {device.location && (
                      <p className="text-sm text-gray-600 mb-2">
                        {device.location}
                      </p>
                    )}
                    <p className="text-xs text-gray-500">
                      Último contato: {device.lastSeen}
                    </p>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mt-8 flex flex-col sm:flex-row gap-4">
          <button
            onClick={handleEnterApp}
            className="flex-1 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center space-x-2"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 7l5 5m0 0l-5 5m5-5H6"
              />
            </svg>
            <span>Acessar Sistema de Controle</span>
          </button>
          <button
            onClick={handleBackToProperties}
            className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Voltar às Propriedades
          </button>
        </div>
      </div>
    </div>
  );
}

export default PropertyDetailsPage;

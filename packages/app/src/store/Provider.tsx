// src/store/Provider.tsx
import { Provider as <PERSON><PERSON><PERSON><PERSON><PERSON>, useAtomValue, useSet<PERSON><PERSON> } from "jotai";
import { useHydrateAtoms } from "jotai/utils";
import React, { useEffect, useState } from "react";
import { useHashLocation } from "wouter/use-hash-location";
import {
  authErrorAtom,
  authLoadingAtom,
  authNetworkErrorAtom,
  initializeAuthAtom,
  isAuthenticatedAtom,
  retryAuthInitializationAtom,
} from "./auth";
import {
  assignAccountAtom,
  availableAccountsAtom,
  fetchAccountsAtom,
  selectedAccountUserIdAtom,
  unassignAccountAtom,
} from "./data";
import { hasActiveLoadingAtom } from "./operations";

// Loading screen component
const AuthLoadingScreen: React.FC = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Carregando...</p>
      </div>
    </div>
  );
};

// Network error screen component
const NetworkErrorScreen: React.FC<{ onRetry: () => void }> = ({ onRetry }) => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center max-w-md mx-auto p-6">
        <div className="mb-6">
          <svg
            className="w-16 h-16 text-red-500 mx-auto mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">
            Erro de Conectividade
          </h2>
          <p className="text-gray-600 mb-6">
            Não foi possível conectar ao servidor. Verifique sua conexão com a
            internet e tente novamente.
          </p>
        </div>
        <button
          onClick={onRetry}
          className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
        >
          Tentar Novamente
        </button>
      </div>
    </div>
  );
};

// Component to handle initialization logic
const AppInitializer: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const initializeAuth = useSetAtom(initializeAuthAtom);
  const retryAuthInitialization = useSetAtom(retryAuthInitializationAtom);
  const fetchAccounts = useSetAtom(fetchAccountsAtom);
  const assignAccount = useSetAtom(assignAccountAtom);
  const isAuthenticated = useAtomValue(isAuthenticatedAtom);
  const authLoading = useAtomValue(authLoadingAtom);
  const authNetworkError = useAtomValue(authNetworkErrorAtom);
  const authError = useAtomValue(authErrorAtom);
  const availableAccounts = useAtomValue(availableAccountsAtom);
  const isGlobalLoading = useAtomValue(hasActiveLoadingAtom);
  const getSelectedAccountUserId = useSetAtom(selectedAccountUserIdAtom);
  const unassignAccount = useSetAtom(unassignAccountAtom);
  const [hasInitialized, setHasInitialized] = useState(false);
  const [accountsInitialized, setAccountsInitialized] = useState(false);
  const [location, setLocation] = useHashLocation();

  const handleRetry = async () => {
    try {
      await retryAuthInitialization();
    } finally {
      setHasInitialized(true);
    }
  };

  useEffect(() => {
    // Initialize auth on app startup - this will check for stored tokens
    // and refresh them if needed, or clear auth state if tokens are invalid
    initializeAuth().finally(() => {
      setHasInitialized(true);
    });
  }, [initializeAuth]);

  // Step 3.3: Account Loading and Verification
  useEffect(() => {
    // Fetch accounts when user is authenticated
    if (isAuthenticated && hasInitialized) {
      console.log("🔄 AppInitializer - Fetching accounts...");
      fetchAccounts()
        .then(() => {
          setAccountsInitialized(true);
        })
        .catch((error) => {
          console.error("Failed to fetch accounts:", error);
          setAccountsInitialized(true); // Still mark as initialized to show UI
        });
    } else if (!isAuthenticated && hasInitialized) {
      // Clear accounts state when user logs out
      setAccountsInitialized(false);
    }
  }, [isAuthenticated, hasInitialized, fetchAccounts]);

  // Core Flow Logic - Step 3.5: Account Flow Decision Logic
  const handleAccountFlowDecision = async (
    accounts: typeof availableAccounts
  ) => {
    try {
      console.log(
        "🔄 Running account flow decision with",
        accounts.length,
        "accounts"
      );

      // Step 3.4: Check if user has any allowed accounts
      if (accounts.length === 0) {
        console.log("❌ No accounts available - redirecting to /no-account");
        setLocation("/no-account");
        return;
      }

      // Step 3.5.1: Check if only one account
      if (accounts.length === 1) {
        console.log("✅ Single account found - auto-selecting");
        const accountUserId = accounts[0].id;
        await assignAccount({ accountUserId, navigate: setLocation });
        return;
      }

      // Step 3.5.2: Multiple accounts - check for previous selection
      console.log("🔍 Multiple accounts - checking previous selection");
      const selectedAccountUserId = getSelectedAccountUserId();
      if (selectedAccountUserId) {
        const isValidPreviousSelection = accounts.find(
          (a) => a.id === selectedAccountUserId
        );
        if (isValidPreviousSelection) {
          console.log(
            "✅ Valid previous selection found - loading account data"
          );
          await assignAccount({
            accountUserId: selectedAccountUserId,
            navigate: setLocation,
            nextLocation: location,
          });
          return;
        } else {
          console.log("❌ Previous selection no longer valid - clearing");
          unassignAccount();
        }
      }

      // Step 3.5.3: No previous selection or invalid - redirect to account selection
      console.log("🔄 Redirecting to account selection");
      setLocation("/select-account");
    } catch (error) {
      console.error("❌ Error in account flow decision:", error);
      // On error, redirect to account selection to let user choose manually
      setLocation("/select-account");
    }
  };

  // Main Flow Controller
  useEffect(() => {
    console.log("🔄 Running main flow controller", {
      isAuthenticated,
      hasInitialized,
      accountsInitialized,
      availableAccounts,
      isGlobalLoading,
    });
    // Only run flow when all prerequisites are met
    if (!isAuthenticated || !hasInitialized || !accountsInitialized) {
      return;
    }

    // Don't run flow if there's global loading (accounts might be loading)
    if (isGlobalLoading) {
      return;
    }

    const currentPath = window.location.hash.replace("#", "") || "/";

    // Don't interfere if user is already in specific flows or pages
    // Let these routes handle their own logic
    const protectedPaths = [
      "/select-account", // User is actively selecting account
      "/no-account", // User is in no-account flow
      "/login", // User is logging in
      "/app", // User is already in the app
      "/select-property", // User is selecting properties
      "/property", // User is viewing/creating properties
    ];

    const shouldRunFlow = !protectedPaths.some((path) =>
      currentPath.startsWith(path)
    );

    if (
      // shouldRunFlow &&
      accountsInitialized &&
      availableAccounts &&
      Array.isArray(availableAccounts)
    ) {
      console.log("🚀 Starting automatic account flow decision");
      handleAccountFlowDecision(availableAccounts);
    }
  }, [isAuthenticated, hasInitialized, accountsInitialized, availableAccounts]);

  // Show network error screen if initialization failed due to network issues
  if (hasInitialized && authNetworkError) {
    return <NetworkErrorScreen onRetry={handleRetry} />;
  }

  // Show loading screen while auth is being initialized
  if (!hasInitialized || authLoading) {
    return <AuthLoadingScreen />;
  }

  return <>{children}</>;
};

// Main store provider component
export const StoreProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return (
    <JotaiProvider>
      <AppInitializer>{children}</AppInitializer>
    </JotaiProvider>
  );
};

// Optional: Hook to provide initial values for SSR or testing
export const useStoreHydration = (initialValues: Array<[any, any]>) => {
  useHydrateAtoms(initialValues);
};

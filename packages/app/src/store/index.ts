// src/store/index.ts
// Export all operations-related atoms
export {
  addErrorOperationAtom,
  addLoadingOperationAtom,
  cleanupErrorsAtom,
  currentLoadingMessageAtom,
  displayErrorsAtom,
  displayLoadingAtom,
  errorOperationsAtom,
  hasActiveLoadingAtom,
  isNetworkError,
  loadingOperations<PERSON>tom,
  removeError<PERSON>peration<PERSON>tom,
  removeLoading<PERSON>peration<PERSON>tom,
  withOperationHandling<PERSON>tom,
  type ErrorOperation,
  type LoadingOperation,
  type OperationOptions,
} from "./operations";

// Export all auth-related atoms
export {
  authErrorAtom,
  authLoadingAtom, // deprecated
  authNetworkErrorAtom,
  authUserAtom,
  initializeAuthAtom,
  isAuthenticatedAtom, // deprecated
  loginAtom,
  logoutAtom,
  retryAuthInitializationAtom,
  type AuthUser,
} from "./auth";

// Export all data-related atoms
export {
  ACCOUNT_USER_TREE_DEBOUNCE_DELAY,
  accountUserTreeAtom,
  assignAccountAtom,
  availableAccountsAtom,
  clearPendingAccountUserTreeRequestsAtom,
  currentAccountAtom,
  devicesAtom,
  devicesByIdAtom,
  fetchAccountsAtom,
  irrigationPlanByIdAtom,
  irrigationPlansAtom,
  irrigationPlansByProjectIdAtom,
  projectByIdAtom,
  projectsAtom,
  propertiesAtom,
  propertyByIdAtom,
  propertyDevicesAtom,
  refetchDataAtom,
  reservoirsAtom,
  reservoirsByIdAtom,
  sectorsByProjectIdAtom,
  selectedAccountUserIdAtom,
  selectedPropertyAtom,
  selectedPropertyIdAtom,
  unassignAccountAtom,
  waterPumpsAtom,
  waterPumpsByIdAtom,
} from "./data";

// Export all CRUD operation atoms
export {
  createIrrigationPlanAtom,
  createIrrigationPlanStepAtom,
  createMeshDeviceMappingAtom,
  createProjectAtom,
  createPropertyAtom,
  createPropertyDeviceAtom,
  createReservoirAtom,
  createSectorAtom,
  createWaterPumpAtom,
  deleteIrrigationPlanStepAtom,
  deleteMeshDeviceMappingAtom,
  deleteReservoirAtom,
  deleteSectorAtom,
  swapIrrigationPlanStepsOrdersAtom,
  updateIrrigationPlanAtom,
  updateIrrigationPlanStepAtom,
  updateMeshDeviceMappingAtom,
  updateProjectAtom,
  updatePropertyAtom,
  updatePropertyDeviceAtom,
  updateReservoirAtom,
  updateSectorAtom,
  updateWaterPumpAtom,
} from "./crud";

// Export all UI-related atoms
export {
  addToastAtom,
  appShell,
  removeAllToastsAtom,
  removeToastAtom,
  showErrorToastAtom,
  showInfoToastAtom,
  showSuccessToastAtom,
  showWarningToastAtom,
  toastsAtom,
  type ToastData,
} from "./ui";

// Helper hooks for common patterns
import { useAtomValue, useSetAtom } from "jotai";
import { useCallback } from "react";
import {
  authUserAtom,
  isAuthenticatedAtom,
  loginAtom,
  logoutAtom,
  //   addToastAtom,
  refetchDataAtom,
} from "./index";

// Custom hooks for common operations
export const useAuth = () => {
  const isAuthenticated = useAtomValue(isAuthenticatedAtom);
  const user = useAtomValue(authUserAtom);
  const login = useSetAtom(loginAtom);
  const logout = useSetAtom(logoutAtom);

  return {
    isAuthenticated,
    user,
    login,
    logout,
  };
};

export const useDataRefresh = () => {
  const refetchData = useSetAtom(refetchDataAtom);

  const refreshData = useCallback(async () => {
    try {
      await refetchData();
    } catch (error) {
      console.error("Failed to refresh data:", error);
    }
  }, [refetchData]);

  return { refreshData };
};

// src/store/ui.ts
import { atom } from "jotai";

export interface ToastData {
  id: string;
  title?: string;
  message: string;
  type?: "success" | "error" | "warning" | "info";
  duration?: number;
  actionLabel?: string;
  onAction?: () => void;
}

// Toast state atoms
export const toastsAtom = atom<ToastData[]>([]);

// Toast actions
export const addToastAtom = atom(
  null,
  (get, set, toastData: Omit<ToastData, "id">) => {
    const id = `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const toast: ToastData = { id, ...toastData };
    const currentToasts = get(toastsAtom);
    set(toastsAtom, [...currentToasts, toast]);
    console.log("Toast added:", toast);
    return id;
  }
);

export const removeToastAtom = atom(null, (get, set, id: string) => {
  const currentToasts = get(toastsAtom);
  set(
    toastsAtom,
    currentToasts.filter((toast) => toast.id !== id)
  );
});

export const removeAllToastsAtom = atom(null, (get, set) => {
  set(toastsAtom, []);
});

// Convenience atoms for different toast types
export const showSuccessToastAtom = atom(
  null,
  (
    get,
    set,
    options: {
      message: string;
      title?: string;
      duration?: number;
      actionLabel?: string;
      onAction?: () => void;
    }
  ) => {
    return set(addToastAtom, { type: "success", ...options });
  }
);

export const showErrorToastAtom = atom(
  null,
  (
    get,
    set,
    options: {
      message: string;
      title?: string;
      duration?: number;
      actionLabel?: string;
      onAction?: () => void;
    }
  ) => {
    return set(addToastAtom, { type: "error", ...options });
  }
);

export const showWarningToastAtom = atom(
  null,
  (
    get,
    set,
    options: {
      message: string;
      title?: string;
      duration?: number;
      actionLabel?: string;
      onAction?: () => void;
    }
  ) => {
    return set(addToastAtom, { type: "warning", ...options });
  }
);

export const showInfoToastAtom = atom(
  null,
  (
    get,
    set,
    options: {
      message: string;
      title?: string;
      duration?: number;
      actionLabel?: string;
      onAction?: () => void;
    }
  ) => {
    return set(addToastAtom, { type: "info", ...options });
  }
);

function appShellAtoms() {
  const backButtonAtom = atom<boolean | string>(false);

  return {
    backButtonAtom,
  };
}

export const appShell = appShellAtoms();

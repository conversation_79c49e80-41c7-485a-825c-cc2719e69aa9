import { useState, useEffect, useRef } from "react";
import { useSet<PERSON>tom } from "jotai";
import Button, { type ButtonProps } from "@/components/ui/Button";
import type { AUTDevice } from "@/api/queries/account";
import { createDeviceMessageRequestAtom } from "@/store/crud";
import { useToast } from "@/components";
import { apiService } from "@/api";

export interface TestLICCommunicationButtonProps
  extends Omit<ButtonProps, "children" | "onClick"> {
  device: AUTDevice;
  pollingInterval?: number;
}

export function TestLICCommunicationButton({
  device,
  pollingInterval = 2000,
  loading: externalLoading = false,
  disabled: externalDisabled = false,
  ...buttonProps
}: TestLICCommunicationButtonProps) {
  const [buttonText, setButtonText] = useState("Testar Comunicação");
  const [isLoading, setIsLoading] = useState(false);
  const [deviceMessageRequestId, setDeviceMessageRequestId] = useState<
    string | null
  >(null);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const createDeviceMessageRequest = useSetAtom(createDeviceMessageRequestAtom);
  const { showError, showSuccess } = useToast();

  // Polling logic using setInterval instead of atom for better control
  useEffect(() => {
    if (!deviceMessageRequestId || !isLoading) {
      return;
    }

    const pollDeviceMessageRequest = async () => {
      try {
        const result = await apiService.deviceMessageRequest.getOne(
          deviceMessageRequestId
        );

        if (result.status === "processing") {
          setButtonText("Processando...");
        } else if (
          result.status === "sent" ||
          result.status === "acknowledged"
        ) {
          setButtonText("Sucesso");
          setIsLoading(false);
          clearInterval(pollingIntervalRef.current!);
          showSuccess({
            message: "Teste de comunicação realizado com sucesso!",
          });
        } else if (result.status === "failed") {
          setButtonText("Erro: Tente novamente");
          setIsLoading(false);
          clearInterval(pollingIntervalRef.current!);
          showError({
            message: "Teste de comunicação falhou. Tente novamente.",
          });
        }
      } catch (error) {
        console.error("Error polling device message request:", error);
        setButtonText("Erro: Tente novamente");
        setIsLoading(false);
        clearInterval(pollingIntervalRef.current!);
        showError({
          message: "Erro ao verificar status do teste de comunicação.",
        });
      }
    };

    // Start polling
    pollingIntervalRef.current = setInterval(
      pollDeviceMessageRequest,
      pollingInterval
    );

    // Initial poll
    pollDeviceMessageRequest();

    // Cleanup on unmount or when polling stops
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
    };
  }, [deviceMessageRequestId, isLoading, pollingInterval]);

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
    };
  }, []);

  const handleClick = async () => {
    console.log("Testing communication for device:", device.identifier);

    setIsLoading(true);
    setButtonText("Testar Comunicação");

    try {
      const result = await createDeviceMessageRequest({
        device: device.id,
        payload_type: "request_info",
      });

      if (result.success && result.data) {
        console.log("Communication test request created successfully:", result);
        setButtonText("Pendente...");
        setDeviceMessageRequestId(result.data.id);
        // Keep loading state - polling will handle state changes
      } else {
        console.error("Communication test request failed:", result.error);
        setButtonText("Erro: Tente novamente");
        setIsLoading(false);
        showError({
          message: `Falha ao criar solicitação de teste: ${result.error}`,
        });
      }
    } catch (error) {
      console.error("Unexpected error during communication test:", error);
      setButtonText("Erro: Tente novamente");
      setIsLoading(false);
      showError({
        message: "Erro inesperado ao criar solicitação de teste",
      });
    }
  };

  const isDisabled = externalDisabled || externalLoading;
  const loading = isLoading || externalLoading;

  return (
    <Button
      {...buttonProps}
      loading={loading}
      disabled={isDisabled}
      onClick={handleClick}
    >
      {buttonText}
    </Button>
  );
}

export default TestLICCommunicationButton;

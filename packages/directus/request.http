@url = http://localhost:8055
@username = <EMAIL>
@password = teste


# @name Login
POST {{url}}/auth/login
Content-Type: application/json

{
  "email": "{{username}}",
  "password": "{{password}}"
}

{{
  $global.token = response.parsedBody.data.access_token;
  $global.refreshToken = response.parsedBody.data.refresh_token;
}}

###
# @name RefreshToken
POST {{url}}/auth/refresh
Content-Type: application/json

{
  "refresh_token": "{{$global.refreshToken}}"
}

{{
  $global.token = response.parsedBody.data.access_token;
  $global.refreshToken = response.parsedBody.data.refresh_token;
}}

###
# @name GetUser
GET {{url}}/users/me
Authorization: Bearer {{$global.token}}

###
# @name GetUsers
GET {{url}}/users
Authorization: Bearer {{$global.token}}

###
# @name GetAccountUserTree
GET {{url}}/items/account_user
  ?fields[]=*
  &fields[]=account.*
  &fields[]=account.owner.id,account.owner.first_name,account.owner.last_name,account.owner.email,account.owner.avatar
  &fields[]=account.properties.*
  &fields[]=account.properties.water_pumps.*
  &fields[]=account.properties.projects.*
  &fields[]=account.properties.projects.irrigation_plans.*
  &fields[]=account.properties.projects.irrigation_plans.steps.*
  &fields[]=account.properties.projects.sectors.*
  &fields[]=account.properties.devices.*
  &fields[]=account.properties.devices.device.*
Authorization: Bearer {{$global.token}}

###
# @name GetAccounts
GET {{url}}/items/account_user
  ?fields[]=id,role,start_date,end_date,account.id,account.owner.id,account.owner.first_name,account.owner.last_name,account.owner.email
Authorization: Bearer {{$global.token}}

###
# @name ListProperties
GET {{url}}/items/property
  ?fields[]=*
Authorization: Bearer {{$global.token}}


###
# @name GetDevices
GET {{url}}/items/device?fields=*
Authorization: Bearer {{$global.token}}

###
# @name GetAccountUsers
GET {{url}}/items/account_user
  ?fields=id,role,start_date,end_date,account.owner.id,account.owner.first_name,account.owner.last_name,account.owner.email
Authorization: Bearer {{$global.token}}


###
# @name ListIrrigationPlans
GET {{url}}/items/irrigation_plan
  ?fields[]=*
Authorization: Bearer {{$global.token}}

###
# @name ListIrrigationPlanSteps
GET {{url}}/items/irrigation_plan_step
  ?fields[]=*
Authorization: Bearer {{$global.token}}

###
# @name ListPropertyDevices
GET {{url}}/items/property_device
  ?fields[]=*
Authorization: Bearer {{$global.token}}

###
# @name ListWaterPumps
GET {{url}}/items/water_pump
  ?fields[]=*
Authorization: Bearer {{$global.token}}

###
# @name ListMeshDeviceMappings
GET {{url}}/items/mesh_device_mapping
  ?fields[]=*
Authorization: Bearer {{$global.token}}


###
# @name ListDeviceMessageRequest
GET {{url}}/items/device_message_request
  ?fields=*
Authorization: Bearer {{$global.token}}

###
# @name PUT
PATCH {{url}}/items/device_message_request/75038ad1-54f6-44fc-acf1-cbdcfd260f73
Authorization: Bearer {{$global.token}}

{
  "date_updated": "$NOW()"
}
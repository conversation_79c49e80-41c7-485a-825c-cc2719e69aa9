/**
 * Business logic for creating irrigation plans and steps
 */

import type { Knex } from "knex";
import type { IrrigationPlanData } from "../types";

/**
 * Create irrigation plans for a project
 */
export async function createIrrigationPlansForProject(
  trx: Knex,
  data: IrrigationPlanData
): Promise<void> {
  for (const planData of data.plansData) {
    const [irrigationPlan] = await trx("irrigation_plan")
      .insert({
        project: data.project,
        name: planData.name,
        description: planData.description,
        start_time: planData.start_time,
        days_of_week: JSON.stringify(planData.days_of_week),
        is_enabled: true,
        fertigation_enabled: Math.random() > 0.5, // Randomly enable fertigation for some plans
        backwash_enabled: Math.random() > 0.5, // Randomly enable backwash for some plans
        start_date: "2024-01-01",
        end_date: null,
      })
      .returning("id");
    const irrigationPlanId = irrigationPlan.id || irrigationPlan;

    // Create irrigation plan steps
    for (const stepData of planData.steps) {
      await trx("irrigation_plan_step").insert({
        irrigation_plan: irrigationPlanId,
        sector: data.sectorIds[stepData.order - 1], // Use order to get correct sector
        description: stepData.description,
        order: stepData.order,
        duration_seconds: stepData.duration_seconds,
        fertigation_start_delay_seconds:
          stepData.fertigation_start_delay_seconds || null,
        fertigation_duration_seconds:
          stepData.fertigation_duration_seconds || null,
      });
    }
  }
}

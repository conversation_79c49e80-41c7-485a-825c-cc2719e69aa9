/**
 * Business logic for creating projects and related entities
 */

import type { Knex } from "knex";
import type { ProjectCreationData, ProjectsForPropertyData } from "../types";
import { 
  createDevice, 
  associateDeviceWithProperty, 
  createWaterPump, 
  createProject,
  createMeshDeviceMapping 
} from "../operations";
import { createSectorsForProject } from "./sector-creation";
import { createIrrigationPlansForProject } from "./irrigation-plan-creation";

/**
 * Create a complete project with all its components
 */
export async function createProjectForProperty(
  trx: Knex,
  data: ProjectCreationData
): Promise<{
  projectId: string;
  licPropertyDeviceId: string;
  irrigationWpcPropertyDeviceId: string;
  fertigationWpcPropertyDeviceId?: string | null;
}> {
  // Create LIC device
  const licDeviceId = await createDevice(trx, data.licDeviceData);
  const licPdId = await associateDeviceWithProperty(trx, {
    device: licDeviceId,
    property: data.property,
  });

  // Create irrigation water pump and its controller
  const irrigationWpcDeviceId = await createDevice(
    trx,
    data.irrigationWpcDeviceData
  );
  const irrigationWpcPdId = await associateDeviceWithProperty(trx, {
    device: irrigationWpcDeviceId,
    property: data.property,
  });

  const irrigationWaterPumpId = await createWaterPump(trx, {
    ...data.irrigationWaterPumpData,
    property: data.property,
    water_pump_controller: irrigationWpcDeviceId,
  });

  // Create fertigation water pump if data provided
  let fertigationWaterPumpId = null;
  let fertigationWpcPdId: string | null = null;
  if (data.fertigationWpcDeviceData && data.fertigationWaterPumpData) {
    const fertigationWpcDeviceId = await createDevice(
      trx,
      data.fertigationWpcDeviceData
    );
    fertigationWpcPdId = await associateDeviceWithProperty(trx, {
      device: fertigationWpcDeviceId,
      property: data.property,
    });

    fertigationWaterPumpId = await createWaterPump(trx, {
      ...data.fertigationWaterPumpData,
      property: data.property,
      water_pump_controller: fertigationWpcDeviceId,
    });
  }

  // Create mesh device mappings BEFORE creating the project to satisfy constraints
  const meshPdIds = [irrigationWpcPdId];
  if (fertigationWpcPdId) {
    meshPdIds.push(fertigationWpcPdId);
  }

  const referenceStart = new Date();
  referenceStart.setDate(referenceStart.getDate() - 10); // active since 10 days ago

  const historyStart = new Date();
  historyStart.setDate(historyStart.getDate() - 30);
  const historyEnd = new Date();
  historyEnd.setDate(historyEnd.getDate() - 20);

  // Create mappings for all mesh devices to the LIC
  for (const meshPd of meshPdIds) {
    // Historical mapping (ended before current period)
    await createMeshDeviceMapping(trx, {
      mesh_property_device: meshPd,
      lic_property_device: licPdId,
      start_date: historyStart,
      end_date: historyEnd,
    });

    // Active mapping (no end_date -> indefinite)
    await createMeshDeviceMapping(trx, {
      mesh_property_device: meshPd,
      lic_property_device: licPdId,
      start_date: referenceStart,
      end_date: null,
    });

    // Update current mesh mapping via DB function
    await trx.raw(
      `SELECT im_update_current_mesh_device_mapping(ARRAY[?]::uuid[], now())`,
      [meshPd]
    );
  }

  // Create the project
  const projectId = await createProject(trx, {
    property: data.property,
    name: data.projectData.name,
    description: data.projectData.description,
    irrigation_water_pump: irrigationWaterPumpId,
    fertigation_water_pump: fertigationWaterPumpId!,
    localized_irrigation_controller: licDeviceId,
    pipe_wash_time_seconds: fertigationWaterPumpId
      ? 5 + Math.floor(Math.random() * 10)
      : undefined, // 5-15 minutes if fertigation enabled
    backwash_duration_seconds: 10 + Math.floor(Math.random() * 20), // 10-30 minutes
    backwash_period_seconds: 240 + Math.floor(Math.random() * 480), // 4-12 hours (240-720 minutes)
  });

  // Create sectors for the project
  const sectorIds = await createSectorsForProject(trx, {
    ...data.sectorsData,
    project: projectId,
    licPropertyDeviceId: licPdId,
    irrigationPumpHasFrequencyInverter: data.irrigationWaterPumpData.has_frequency_inverter,
  });

  // Create irrigation plans for the project
  await createIrrigationPlansForProject(trx, {
    ...data.irrigationPlansData,
    project: projectId,
    sectorIds,
  });

  return {
    projectId,
    licPropertyDeviceId: licPdId,
    irrigationWpcPropertyDeviceId: irrigationWpcPdId,
    fertigationWpcPropertyDeviceId: fertigationWpcPdId,
  };
}

/**
 * Create multiple projects for a property
 */
export async function createProjectsForProperty(
  trx: Knex,
  data: ProjectsForPropertyData
): Promise<{
  projectIds: string[];
  licPdIds: string[];
  meshPdIds: string[];
}> {
  const projectIds: string[] = [];
  const licPdIds: string[] = [];
  const meshPdIds: string[] = [];

  for (const projectData of data.projectsData) {
    const created = await createProjectForProperty(trx, projectData);
    projectIds.push(created.projectId);
    licPdIds.push(created.licPropertyDeviceId);
    meshPdIds.push(created.irrigationWpcPropertyDeviceId);
    if (created.fertigationWpcPropertyDeviceId) {
      meshPdIds.push(created.fertigationWpcPropertyDeviceId);
    }
  }
  return { projectIds, licPdIds, meshPdIds };
}

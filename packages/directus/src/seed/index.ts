import { program } from "commander";
import knex, { <PERSON><PERSON> } from "knex";
import {
  generateHexId,
  generateDeviceIdentifier,
  generateHash,
  randomDaysOfWeek,
} from "./utils";
import type { DeviceData, ProjectCreationData } from "./types";
import {
  createDevice,
  associateDeviceWithProperty,
  createMeshDeviceMapping,
  createWaterPump,
} from "./operations";
import {
  createProjectsForProperty,
  createReservoirsForProperty,
  createUsersAndAccounts,
  createProperties,
  createAccountUserRelationships,
  createTestUser,
} from "./business-logic";

// Helper functions with consistent signature pattern

/**
 * Seed the database with initial data for development purposes.
 * @param trx
 */
export async function seedDatabase(trx: Knex) {
  // 1. Check for existing users
  const existingUsers = await trx("directus_users").whereIn("email", [
    "<EMAIL>",
    "<EMAIL>",
  ]);
  if (existingUsers.length > 0) {
    console.log("Seed users already exist. Aborting seeding.");
    throw new Error("Seed users already exist.");
  }

  // 2. Create users and accounts
  const passwordHash = await generateHash("password123");
  const { users, accounts } = await createUsersAndAccounts(trx, {
    passwordHash,
    usersData: [
      {
        first_name: "Joaquim",
        last_name: "Silva",
        email: "<EMAIL>",
        password: passwordHash,
      },
      {
        first_name: "Maria",
        last_name: "Oliveira",
        email: "<EMAIL>",
        password: passwordHash,
      },
    ],
  });

  // 3. Create properties
  const properties = await createProperties(trx, {
    joaquimAccountId: accounts.joaquimAccountId!,
    mariaAccountId: accounts.mariaAccountId!,
    propertiesData: [
      {
        account: accounts.joaquimAccountId!,
        name: "Fazenda Sol Nascente",
        address_postal_code: "12345-678",
        address_street_name: "Estrada Rural",
        address_street_number: "100",
        address_city: "Cidade Sol",
        address_state: "SP",
        backwash_duration_minutes: 15,
        backwash_period_minutes: 480, // 8 hours
        backwash_delay_seconds: 30,
        rain_gauge_enabled: true,
        rain_gauge_resolution_mm: 0.2,
        precipitation_volume_limit_mm: 2.5,
        precipitation_suspended_duration_hours: 12,
      },
      {
        account: accounts.mariaAccountId!,
        name: "Sítio Bela Vista",
        address_postal_code: "23456-789",
        address_street_name: "Rua das Flores",
        address_street_number: "200",
        address_city: "Florada",
        address_state: "MG",
        backwash_duration_minutes: 20,
        backwash_period_minutes: 720, // 12 hours
        backwash_delay_seconds: 45,
        rain_gauge_enabled: false,
        rain_gauge_resolution_mm: 0.2,
        precipitation_volume_limit_mm: 3.0,
        precipitation_suspended_duration_hours: 18,
      },
      {
        account: accounts.mariaAccountId!,
        name: "Chácara das Flores",
        address_postal_code: "34567-890",
        address_street_name: "Alameda das Rosas",
        address_street_number: "300",
        address_city: "Jardim",
        address_state: "RJ",
        backwash_duration_minutes: 10,
        backwash_period_minutes: 360, // 6 hours
        backwash_delay_seconds: 60,
        rain_gauge_enabled: true,
        rain_gauge_resolution_mm: 0.1,
        precipitation_volume_limit_mm: 1.5,
        precipitation_suspended_duration_hours: 24,
      },
    ],
  });

  // 4. Create projects for each property
  // Track PDs per property to respect mapping constraints
  const perPropertyLicPdIds: Record<string, string[]> = {};
  const perPropertyMeshPdIds: Record<string, string[]> = {};

  for (const property of properties) {
    // Generate project data for this property
    const projectsData: ProjectCreationData[] = [];
    const projectPlaceKinds = [
      "Lago",
      "Lagoa",
      "Represa",
      "Açude",
      "Córrego",
      "Rio",
      "Riacho",
    ];
    const projectPlaces = [
      "ao Norte",
      "ao Sul",
      "ao Leste",
      "ao Oeste",
      "perto do Pasto",
      "perto da Mata",
      "perto da Casa",
      "perto do Pomar",
      "perto da Horta",
      "perto do Viveiro",
      "perto do Curral",
      "perto do Brejo",
      "perto do Poço",
    ];
    const projectNames = new Set<string>();
    for (let i = 0; i < 2; i++) {
      let projectName: string;
      // Generate a unique project name for the property
      do {
        const projectPlaceKind =
          projectPlaceKinds[
            Math.floor(Math.random() * projectPlaceKinds.length)
          ]!;
        const projectPlace =
          projectPlaces[Math.floor(Math.random() * projectPlaces.length)]!;
        projectName = `${projectPlaceKind} ${projectPlace}`;
      } while (projectNames.has(projectName));
      projectNames.add(projectName);

      // Create VC devices data (1 per 4 sectors)
      const sectorsCount = 6;
      const vdDevicesData: DeviceData[] = [];
      for (let vcIndex = 0; vcIndex < Math.ceil(sectorsCount / 4); vcIndex++) {
        vdDevicesData.push({
          model: "VC",
          identifier: generateDeviceIdentifier("VC"),
          notes: `Controlador de válvulas ${vcIndex + 1} para ${projectName}`,
        });
      }

      // Create sectors data
      const sectorsData: Array<{
        name: string;
        description: string;
        valveControllerIndex: number;
        valve_controller_output: number;
        area: number;
      }> = [];
      for (let sectorIndex = 0; sectorIndex < sectorsCount; sectorIndex++) {
        sectorsData.push({
          name: `Setor ${sectorIndex + 1}`,
          description: `Área de irrigação ${sectorIndex + 1} do ${projectName}`,
          valveControllerIndex: Math.floor(sectorIndex / 4),
          valve_controller_output: (sectorIndex % 4) + 1,
          area: 1.5 + sectorIndex * 0.3,
        });
      }

      // Generate irrigation plans data
      const plansCount = Math.ceil(sectorsCount / 4);
      const plansData = [];
      for (let planIndex = 0; planIndex < plansCount; planIndex++) {
        const steps = [];
        const sectorsForThisPlan = Math.min(4, sectorsCount - planIndex * 4);

        for (let stepIndex = 0; stepIndex < sectorsForThisPlan; stepIndex++) {
          const hasFerti = Math.random() > 0.5; // Randomly enable fertigation for some steps
          steps.push({
            description: `Irrigação do setor ${stepIndex + 1} do plano ${
              planIndex + 1
            }`,
            order: stepIndex + 1,
            duration_seconds: 1800 + stepIndex * 300,
            fertigation_start_delay_seconds: hasFerti
              ? 300 + Math.floor(Math.random() * 300)
              : undefined, // 5-10 minutes delay
            fertigation_duration_seconds: hasFerti
              ? 600 + Math.floor(Math.random() * 600)
              : undefined, // 10-20 minutes duration
          });
        }

        plansData.push({
          name: `Plano Irrigação ${planIndex + 1}`,
          description: `Plano de irrigação automatizada ${
            planIndex + 1
          } para ${projectName}`,
          start_time: `0${6 + planIndex}:00:00`,
          days_of_week: randomDaysOfWeek(),
          steps,
        });
      }

      const projectData: ProjectCreationData = {
        property: property.id,
        propertyName: property.name,
        projectIndex: i,
        projectData: {
          name: projectName,
          description: `Sistema de irrigação automatizada para a área ${
            i === 0 ? "norte" : "sul"
          } da propriedade`,
        },
        licDeviceData: {
          model: "LIC",
          identifier: generateDeviceIdentifier("LIC"),
          notes: `Controlador de irrigação localizado para ${projectName}`,
        },
        irrigationWpcDeviceData: {
          model: "WPC-PL10",
          identifier: generateDeviceIdentifier("WPC-PL10"),
          notes: `Controlador da bomba de irrigação para ${projectName}`,
        },
        irrigationWaterPumpData: {
          label: `Bomba Irrigação ${i + 1}`,
          identifier: generateHexId(6),
          pump_type: "IRRIGATION",
          pump_model: "Modelo ABC-123",
          has_frequency_inverter: Math.random() > 0.5,
          monitor_operation: Math.random() > 0.3,
          mode: Math.random() > 0.5 ? "PULSE" : "CONTINUOUS",
        },
        sectorsData: {
          project: "", // Filled in createProjectForProperty
          property: property.id,
          projectName,
          propertyName: property.name,
          projectIndex: i,
          sectorsCount,
          vdDevicesData,
          sectorsData,
        },
        irrigationPlansData: {
          plansData,
        },
      };

      // Add fertigation data only for first project
      if (i === 0) {
        projectData.fertigationWpcDeviceData = {
          model: "WPC-PL50",
          identifier: generateDeviceIdentifier("WPC-PL50"),
          notes: `Controlador da bomba de fertirrigação para ${projectName}`,
        };
        projectData.fertigationWaterPumpData = {
          label: `Bomba Fertirrigação ${i + 1}`,
          identifier: generateHexId(6),
          pump_type: "FERTIGATION",
          pump_model: "Modelo XYZ-456",
          has_frequency_inverter: Math.random() > 0.5,
          monitor_operation: Math.random() > 0.3,
          mode: Math.random() > 0.5 ? "PULSE" : "CONTINUOUS",
        };
      }

      projectsData.push(projectData);
    }

    const created = await createProjectsForProperty(trx, {
      property: property.id,
      propertyName: property.name,
      projectsData,
    });

    // Save LIC and mesh PD ids grouped by property
    perPropertyLicPdIds[property.id] = [
      ...(perPropertyLicPdIds[property.id] || []),
      ...created.licPdIds,
    ];
    perPropertyMeshPdIds[property.id] = [
      ...(perPropertyMeshPdIds[property.id] || []),
      ...created.meshPdIds,
    ];

    // Create RM devices for reservoir monitoring
    const rmDeviceIds = [];
    const rmPdIds = []; // Track RM property_device IDs for mesh mappings
    const rmDeviceCount = Math.floor(Math.random() * 3) + 1; // 1-3 RM devices per property

    for (let i = 0; i < rmDeviceCount; i++) {
      const rmDeviceId = await createDevice(trx, {
        model: "RM",
        identifier: generateDeviceIdentifier("RM"),
        notes: `Monitor de reservatório ${i + 1} para ${property.name}`,
      });

      const rmPdId = await associateDeviceWithProperty(trx, {
        device: rmDeviceId,
        property: property.id,
      });

      rmDeviceIds.push(rmDeviceId);
      rmPdIds.push(rmPdId); // Track for mesh mappings
    }

    // Create SERVICE water pumps for reservoirs
    const servicePumpIds = [];
    const serviceWpcPdIds = []; // Track service pump controller property_device IDs for mesh mappings
    const servicePumpCount = Math.floor(Math.random() * 2) + 1; // 1-2 service pumps per property

    for (let i = 0; i < servicePumpCount; i++) {
      // Create WPC device for service pump
      const serviceWpcDeviceId = await createDevice(trx, {
        model: "WPC-PL10",
        identifier: generateDeviceIdentifier("WPC-PL10"),
        notes: `Controlador da bomba de serviço ${i + 1} para ${property.name}`,
      });

      const serviceWpcPdId = await associateDeviceWithProperty(trx, {
        device: serviceWpcDeviceId,
        property: property.id,
      });

      serviceWpcPdIds.push(serviceWpcPdId); // Track for mesh mappings

      // Create service water pump
      const servicePumpId = await createWaterPump(trx, {
        property: property.id,
        water_pump_controller: serviceWpcDeviceId,
        label: `Bomba Serviço ${i + 1}`,
        identifier: generateHexId(6),
        pump_type: "SERVICE",
        pump_model: "Modelo SRV-789",
        has_frequency_inverter: Math.random() > 0.6,
        monitor_operation: Math.random() > 0.4,
        mode: Math.random() > 0.5 ? "PULSE" : "CONTINUOUS",
      });

      servicePumpIds.push(servicePumpId);
    }

    // Create mesh device mappings for service pumps and RM devices
    // This must happen BEFORE creating reservoirs to satisfy constraints
    const licPdIds = perPropertyLicPdIds[property.id] || [];
    const allNonProjectMeshPdIds = [...serviceWpcPdIds, ...rmPdIds];

    if (allNonProjectMeshPdIds.length > 0 && licPdIds.length > 0) {
      const referenceStart = new Date();
      referenceStart.setDate(referenceStart.getDate() - 10); // active since 10 days ago

      const historyStart = new Date();
      historyStart.setDate(historyStart.getDate() - 30);
      const historyEnd = new Date();
      historyEnd.setDate(historyEnd.getDate() - 20);

      // Map all service pumps and RM devices to the same LIC to ensure reservoir constraints are satisfied
      // Use the first available LIC for all non-project devices
      const primaryLicPd = licPdIds[0]!;

      for (const meshPd of allNonProjectMeshPdIds) {
        // Historical mapping (ended before current period)
        await createMeshDeviceMapping(trx, {
          mesh_property_device: meshPd,
          lic_property_device: primaryLicPd,
          start_date: historyStart,
          end_date: historyEnd,
        });

        // Active mapping (no end_date -> indefinite)
        await createMeshDeviceMapping(trx, {
          mesh_property_device: meshPd,
          lic_property_device: primaryLicPd,
          start_date: referenceStart,
          end_date: null,
        });

        // Update current mesh mapping via DB function for each mesh PD
        await trx.raw(
          `SELECT im_update_current_mesh_device_mapping(ARRAY[?]::uuid[], now())`,
          [meshPd]
        );
      }
    }

    // Create reservoirs for the property
    await createReservoirsForProperty(trx, {
      property: property.id,
      propertyName: property.name,
      rmDeviceIds,
      servicePumpIds,
    });
  }

  // 5. Create account user relationships
  await createAccountUserRelationships(trx, {
    relationshipsData: [
      {
        account: accounts.joaquimAccountId!,
        user: users.joaquimUserId!,
      },
      {
        account: accounts.mariaAccountId!,
        user: users.mariaUserId!,
      },
      {
        account: accounts.mariaAccountId!,
        user: users.joaquimUserId!,
      },
      {
        account: accounts.joaquimAccountId!,
        user: users.mariaUserId!,
      },
    ],
  });

  // 6. Create test user
  await createTestUser(trx, {
    joaquimAccountId: accounts.joaquimAccountId!,
    mariaAccountId: accounts.mariaAccountId!,
    userData: {
      first_name: "Test",
      last_name: "User",
      email: "<EMAIL>",
      password: await generateHash("teste"),
    },
  });

  // Note: Mesh device mappings are now created per-property during project creation
  // This ensures constraints are satisfied when creating projects, reservoirs, and sectors
}

/**
 * Cleanup the database by removing all seed data.
 * Do not erase data not created by the seed script.
 * @param knex
 */
async function cleanupSeedData(knex: Knex) {
  // 1. Find seed users
  const seedEmails = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
  ];
  const users = await knex("directus_users")
    .whereIn("email", seedEmails)
    .select("id");
  if (users.length === 0) {
    console.log("Seed users not found. No cleanup necessary.");
    return;
  }
  const userIds = users.map((u) => u.id);

  // 2. Find accounts owned by seed users
  const accounts = await knex("account").whereIn("owner", userIds).select("id");
  const accountIds = accounts.map((a) => a.id);

  // 3. Find properties belonging to seed accounts
  const properties = await knex("property")
    .whereIn("account", accountIds)
    .select("id");
  const propertyIds = properties.map((p) => p.id);

  // 4. Find projects belonging to seed properties
  const projects = await knex("project")
    .whereIn("property", propertyIds)
    .select("id");
  const projectIds = projects.map((p) => p.id);

  // 5. Find sectors belonging to seed projects
  const sectors = await knex("sector")
    .whereIn("project", projectIds)
    .select("id");
  const sectorIds = sectors.map((s) => s.id);

  // 6. Find irrigation plans belonging to seed projects
  const irrigationPlans = await knex("irrigation_plan")
    .whereIn("project", projectIds)
    .select("id");
  const irrigationPlanIds = irrigationPlans.map((ip) => ip.id);

  // 7. Find water pumps belonging to seed properties
  const waterPumps = await knex("water_pump")
    .whereIn("property", propertyIds)
    .select("id");
  const waterPumpIds = waterPumps.map((wp) => wp.id);

  // 8. Find reservoirs belonging to seed properties
  const reservoirs = await knex("reservoir")
    .whereIn("property", propertyIds)
    .select("id");
  const reservoirIds = reservoirs.map((r) => r.id);

  // 9. Find devices associated with seed properties
  const propertyDevices = await knex("property_device")
    .whereIn("property", propertyIds)
    .select("device");
  const deviceIds = propertyDevices.map((pd) => pd.device);

  // Additional cleanup for mesh_device_mapping referencing PDs from seed properties
  const seedPropertyDeviceIds = await knex("property_device")
    .whereIn("property", propertyIds)
    .select("id");
  const pdIds = seedPropertyDeviceIds.map((r) => r.id);

  console.log("Cleaning up mesh_device_mapping...");
  await knex("mesh_device_mapping")
    .whereIn("mesh_property_device", pdIds)
    .orWhereIn("lic_property_device", pdIds)
    .del();

  // Delete data in reverse order of dependencies
  console.log("Cleaning up irrigation_plan_step...");
  await knex("irrigation_plan_step")
    .whereIn("irrigation_plan", irrigationPlanIds)
    .del();

  console.log("Cleaning up irrigation_plan...");
  await knex("irrigation_plan").whereIn("id", irrigationPlanIds).del();

  console.log("Cleaning up sector...");
  await knex("sector").whereIn("id", sectorIds).del();

  console.log("Cleaning up project...");
  await knex("project").whereIn("id", projectIds).del();

  console.log("Cleaning up reservoir...");
  await knex("reservoir").whereIn("id", reservoirIds).del();

  console.log("Cleaning up water_pump...");
  await knex("water_pump").whereIn("id", waterPumpIds).del();

  console.log("Cleaning up property_device...");
  await knex("property_device").whereIn("property", propertyIds).del();

  console.log("Cleaning up device...");
  await knex("device").whereIn("id", deviceIds).del();

  console.log("Cleaning up property...");
  await knex("property").whereIn("id", propertyIds).del();

  console.log("Cleaning up account_user...");
  await knex("account_user").whereIn("account", accountIds).del();

  console.log("Cleaning up account...");
  await knex("account").whereIn("id", accountIds).del();

  console.log("Cleaning up directus_users...");
  await knex("directus_users").whereIn("id", userIds).del();
}

async function main() {
  program
    .option("--db-host <host>", "Database host")
    .option("--db-port <port>", "Database port")
    .option("--db-user <user>", "Database user")
    .option("--db-password <password>", "Database password")
    .option("--db-name <n>", "Database name")
    .option("--cleanup", "Cleanup seed data")
    .description("Seed the database with initial data for development purposes")
    .parse(process.argv);

  const options = program.opts();

  const knexInstance = knex({
    client: "pg",
    connection: {
      host: options.dbHost || process.env.DB_HOST,
      port: options.dbPort || parseInt(process.env.DB_PORT || "5432"),
      user: options.dbUser || process.env.DB_USER,
      password: options.dbPassword || process.env.DB_PASSWORD,
      database: options.dbName || process.env.DB_DATABASE,
    },
  });
  try {
    await knexInstance.transaction(async (trx) => {
      if (options.cleanup) {
        console.log("Cleaning up seed data...");
        await cleanupSeedData(trx);
        console.log("Seed data cleaned up successfully.");
        return;
      }
      console.log("Seeding database...");
      await seedDatabase(trx);
      console.log("Database seeded successfully.");
    });
  } catch (error) {
    console.error("Error seeding database:", error);
  } finally {
    await knexInstance.destroy();
  }
}

if (import.meta.main) {
  main();
}

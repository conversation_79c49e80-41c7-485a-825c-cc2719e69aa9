import { SQL } from "bun";
import { config } from "../../src/config";

export const db = new SQL(
  config.test_postgres.url ||
    "postgres://postgres:@localhost:5432/irriga_mais?sslmode=prefer",
  {
    max: 20,
    idleTimeout: 30,
    connectionTimeout: 30,
  }
);

/**
 * Begin a transaction for a test, executes the function, and rolls back  .
 */
export async function runInTransaction<T>(
  fn: SQL.TransactionContextCallback<T>,
  trxOrDb?: SQL
) {
  const _db = trxOrDb ?? db;
  return _db.transaction(async (trx) => {
    let result: { success: T } | { error: unknown } | null = null;
    try {
      const res = await fn(trx);
      result = { success: res };
    } catch (error) {
      result = { error };
    }
    if ("error" in result) {
      console.error("Transaction error:", result.error);
      throw result.error;
    }
    await trx`ROLLBACK;`;
    return result.success;
  });
}

TEST_POSTGRES_URL=postgres://postgres:password@localhost:5435/test_db
POSTGRES_URL=postgres://postgres:password@localhost:5435/irrigacao_localizada
MQTT_BROKER_URL=mqtt://mosquitto-codec.saas.byagro.dev.br:8003
MQTT_USERNAME=user
MQTT_PASSWORD=******
LOG_LEVEL=info
CHILDREN_LOG_LEVEL=CodecManager:debug
DIRECTUS_URL=http://localhost:8055
DIRECTUS_AUTH_TOKEN=your_directus_auth_token
DEVICE_MESSAGE_QUEUE_POLLING_INTERVAL_MS=1000
DEVICE_MESSAGE_QUEUE_BATCH_SIZE=50
DEVICE_MESSAGE_QUEUE_RETRY_BATCH_SIZE=20
DEVICE_MESSAGE_QUEUE_ENABLE_RETRY_PROCESSING=true
import type { S<PERSON> } from "bun";
import { LoggerManager } from "../log";
import { CodecManager } from "./codec-manager";
import type { ICodecTransportFactory } from "../transport/types";
import EventEmitter from "events";
import { listLICS } from "../db/queries/lic-queries";
import type { DeviceMessageRequestWithDevice } from "../db/queries/types";

export class CodecManagerRegistry {
  private static readonly log = LoggerManager.getLogger("CodecManagerRegistry");
  private managers: Map<string, Promise<CodecManager>> = new Map();
  private db: SQL | null = null;
  private transportFactory: ICodecTransportFactory | null = null;
  private referenceDate: Date | null = null;
  private emitter = new EventEmitter<{
    codecManagerLoadSuccess: [codecManager: CodecManager];
    codecManagerLoadError: [
      error: Error,
      licIdentifier: string,
      referenceDate: Date
    ];
  }>();
  async init(
    db: SQL,
    transportFactory: ICodecTransportFactory,
    referenceDate: Date
  ) {
    // Store references for later use in event handlers
    this.db = db;
    this.transportFactory = transportFactory;
    this.referenceDate = referenceDate;

    CodecManagerRegistry.log.info("CodecManagerRegistry initialized");
    const lics = await listLICS(db);
    CodecManagerRegistry.log.info(`Found ${lics.length} LICs`);
    for (const lic of lics) {
      this.get(db, lic.identifier, transportFactory, referenceDate);
    }
  }

  async get(
    db: SQL,
    licIdentifier: string,
    transportFactory: ICodecTransportFactory,
    referenceDate: Date
  ): Promise<CodecManager> {
    const key = licIdentifier;
    if (!this.managers.has(key)) {
      CodecManagerRegistry.log.info(`Creating new CodecManager for ${key}`);
      const promise = CodecManager.loadFromDB(
        db,
        licIdentifier,
        transportFactory,
        referenceDate
      )
        .then((r) => {
          if (!r) {
            this.managers.delete(key);
            throw new Error("Failed to load CodecManager: " + key);
          }
          CodecManagerRegistry.log.info(`Loaded CodecManager for ${key}`);
          this.emitter.emit("codecManagerLoadSuccess", r);
          return r;
        })
        .catch((err) => {
          this.managers.delete(key);
          this.emitter.emit("codecManagerLoadError", err, key, referenceDate);
          throw err;
        });
      this.managers.set(key, promise);
    }
    return await this.managers.get(key)!;
  }

  /**
   * Handle device_message_request event from DeviceMessageQueueService.
   * Routes the message to the appropriate CodecManager for sending.
   *
   * @param messageRequest - Device message request with device info
   */
  async handleDeviceMessageRequest(
    messageRequest: DeviceMessageRequestWithDevice
  ): Promise<void> {
    if (!this.db || !this.transportFactory || !this.referenceDate) {
      CodecManagerRegistry.log.error(
        "CodecManagerRegistry not properly initialized - missing db, transportFactory, or referenceDate"
      );
      return;
    }

    const deviceIdentifier = messageRequest.device.identifier;

    try {
      CodecManagerRegistry.log.debug(
        `Handling device message request ${messageRequest.id} for device ${deviceIdentifier}`
      );

      // Get or create CodecManager for the device
      const codecManager = await this.get(
        this.db,
        deviceIdentifier,
        this.transportFactory,
        this.referenceDate
      );

      // Send the message using the CodecManager
      const result = await codecManager.sendDeviceMessageRequest(
        messageRequest
      );

      if (result.success) {
        CodecManagerRegistry.log.debug(
          `Successfully processed message request ${messageRequest.id} for device ${deviceIdentifier}`
        );
      } else {
        CodecManagerRegistry.log.warn(
          `Failed to process message request ${messageRequest.id} for device ${deviceIdentifier}: ${result.error}`
        );
      }
    } catch (error) {
      CodecManagerRegistry.log.error(
        `Error handling device message request ${messageRequest.id} for device ${deviceIdentifier}:`,
        error
      );
    }
  }

  /**
   * Handle device_message_retry event from DeviceMessageQueueService.
   * This is similar to handleDeviceMessageRequest but for retry scenarios.
   *
   * @param messageRequest - Device message request with device info
   */
  async handleDeviceMessageRetry(
    messageRequest: DeviceMessageRequestWithDevice
  ): Promise<void> {
    CodecManagerRegistry.log.info(
      `Retrying message request ${messageRequest.id} for device ${
        messageRequest.device.identifier
      } (attempt ${messageRequest.attempts + 1}/${messageRequest.max_attempts})`
    );

    // Use the same handler as regular requests
    await this.handleDeviceMessageRequest(messageRequest);
  }

  /**
   * Get the CodecManager for a specific device identifier.
   * This is a convenience method that uses the stored initialization parameters.
   *
   * @param deviceIdentifier - Device identifier (e.g., "9C821FA2B9E8")
   * @returns Promise that resolves to CodecManager or throws if not found
   */
  async getCodecManagerByDeviceIdentifier(
    deviceIdentifier: string
  ): Promise<CodecManager> {
    if (!this.db || !this.transportFactory || !this.referenceDate) {
      throw new Error(
        "CodecManagerRegistry not properly initialized - call init() first"
      );
    }

    return await this.get(
      this.db,
      deviceIdentifier,
      this.transportFactory,
      this.referenceDate
    );
  }

  /**
   * Get all currently loaded CodecManager instances.
   *
   * @returns Array of loaded CodecManager instances
   */
  async getAllLoadedCodecManagers(): Promise<CodecManager[]> {
    const managers: CodecManager[] = [];

    for (const [identifier, managerPromise] of this.managers.entries()) {
      try {
        const manager = await managerPromise;
        managers.push(manager);
      } catch (error) {
        CodecManagerRegistry.log.warn(
          `Failed to get CodecManager for ${identifier}:`,
          error
        );
      }
    }

    return managers;
  }
}

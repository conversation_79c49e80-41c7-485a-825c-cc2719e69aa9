/**
 * Individual Device Generators for IrrigaNet Database Loaders
 *
 * This module contains functions for creating individual device configurations
 * that are attached to mesh devices. Each function handles a specific type
 * of individual device following the Single Responsibility principle.
 */

import type {
  ProjectWithSchedulingData,
  ReservoirWithMonitorAndWaterPumpWithController,
  SectorWithValveController,
  WaterPumpWithController,
} from "../../db/queries/types";
import type {
  IrriganetDeviceWithElementId,
  IrriganetMeshDeviceWithDeviceId,
} from "./types";
import { DEV_TYPES, MONI, PULSE } from "./types";
import { IdGenerator } from "./utilities";

// ==============================================
// IRRIGATION DEVICE GENERATION
// ==============================================

/**
 * Add irrigation devices to a mesh device
 *
 * @param deviceIdGen - ID generator for individual devices
 * @param devices - Array to add devices to
 * @param pumpMeshDevice - Irrigation pump mesh device
 * @param project - Project data
 * @param fertigationAndIrrigationControllerShared - Whether controllers are shared
 */
export function addIrrigationDevices(
  deviceIdGen: IdGenerator,
  devices: IrriganetDeviceWithElementId[],
  pumpMeshDevice: IrriganetMeshDeviceWithDeviceId,
  project: ProjectWithSchedulingData,
  fertigationAndIrrigationControllerShared: boolean
): void {
  const irrigationPump = project.irrigation_water_pump;
  const pumpController = irrigationPump.water_pump_controller;

  if (pumpController.model === "WPC-PL10") {
    addIrrigationDevicesForPL10(
      deviceIdGen,
      devices,
      pumpMeshDevice,
      project,
      fertigationAndIrrigationControllerShared
    );
  } else {
    addIrrigationDevicesForPL50(
      deviceIdGen,
      devices,
      pumpMeshDevice,
      project,
      fertigationAndIrrigationControllerShared
    );
  }
}

/**
 * Add irrigation devices for WPC-PL10 controller
 */
function addIrrigationDevicesForPL10(
  deviceIdGen: IdGenerator,
  devices: IrriganetDeviceWithElementId[],
  pumpMeshDevice: IrriganetMeshDeviceWithDeviceId,
  project: ProjectWithSchedulingData,
  fertigationAndIrrigationControllerShared: boolean
): void {
  const irrigationPump = project.irrigation_water_pump;

  // Add irrigation pump device
  const irrigDeviceIdx = deviceIdGen.next();
  devices.push({
    idx: irrigDeviceIdx,
    mesh_idx: pumpMeshDevice.idx,
    identity: "0",
    type: DEV_TYPES.IrrigationPump,
    out1: 1,
    out2: 0,
    input: pumpMeshDevice.check_input ? 1 : 0,
    mode:
      (irrigationPump.mode === "PULSE" ? PULSE : 0) |
      (irrigationPump.monitor_operation ? MONI : 0),
    sector: undefined,
    ord_idx: irrigDeviceIdx - 1,
    eqpt_ver: undefined,
    power: undefined,
    elementType: "pump",
    elementId: irrigationPump.id,
  });

  // Add fertigation device if controller is shared
  if (fertigationAndIrrigationControllerShared) {
    addSharedFertigationDevice(
      deviceIdGen,
      devices,
      pumpMeshDevice,
      irrigationPump.id
    );
  }

  // Add backwash device if irrigation type
  if (project.backwash_pump_type === "IRRIGATION") {
    addBackwashDevice(
      deviceIdGen,
      devices,
      pumpMeshDevice,
      "2",
      irrigationPump.id
    );
  }
}

/**
 * Add irrigation devices for WPC-PL50 controller
 */
function addIrrigationDevicesForPL50(
  deviceIdGen: IdGenerator,
  devices: IrriganetDeviceWithElementId[],
  pumpMeshDevice: IrriganetMeshDeviceWithDeviceId,
  project: ProjectWithSchedulingData,
  fertigationAndIrrigationControllerShared: boolean
): void {
  const irrigationPump = project.irrigation_water_pump;

  // Add irrigation pump device
  const irrigDeviceIdx = deviceIdGen.next();
  devices.push({
    idx: irrigDeviceIdx,
    mesh_idx: pumpMeshDevice.idx,
    identity: "0",
    type: DEV_TYPES.IrrigationPump,
    out1: 1,
    out2: irrigationPump.mode === "PULSE" ? 2 : 0,
    input: pumpMeshDevice.check_input ? 1 : 0,
    mode:
      (irrigationPump.mode === "PULSE" ? PULSE : 0) |
      (irrigationPump.monitor_operation ? MONI : 0),
    sector: undefined,
    ord_idx: irrigDeviceIdx - 1,
    eqpt_ver: undefined,
    power: undefined,
    elementType: "pump",
    elementId: irrigationPump.id,
  });

  // PL50 fertigation only works in CONTINUOUS mode and when controller is shared
  if (
    fertigationAndIrrigationControllerShared &&
    irrigationPump.mode === "CONTINUOUS"
  ) {
    addSharedFertigationDevice(
      deviceIdGen,
      devices,
      pumpMeshDevice,
      irrigationPump.id
    );
  }

  // PL50 has no backwash pump support
}

// ==============================================
// FERTIGATION DEVICE GENERATION
// ==============================================

/**
 * Add fertigation devices to a dedicated fertigation mesh device
 *
 * @param deviceIdGen - ID generator for individual devices
 * @param devices - Array to add devices to
 * @param fertiMeshDevice - Fertigation mesh device
 * @param project - Project data
 */
export function addFertigationDevices(
  deviceIdGen: IdGenerator,
  devices: IrriganetDeviceWithElementId[],
  fertiMeshDevice: IrriganetMeshDeviceWithDeviceId,
  project: ProjectWithSchedulingData
): void {
  const fertigationPump = project.fertigation_water_pump;
  if (!fertigationPump) {
    return;
  }

  const pumpController = fertigationPump.water_pump_controller;

  // Add fertigation device for PL10 or PL50 in CONTINUOUS mode
  if (
    pumpController.model === "WPC-PL10" ||
    fertigationPump.mode === "CONTINUOUS"
  ) {
    const fertiDeviceIdx = deviceIdGen.next();
    devices.push({
      idx: fertiDeviceIdx,
      mesh_idx: fertiMeshDevice.idx,
      identity: "1",
      type: DEV_TYPES.Ferti,
      out1: 2,
      out2: 0,
      input: 0,
      mode: 0,
      sector: undefined,
      ord_idx: fertiDeviceIdx - 1,
      eqpt_ver: undefined,
      power: undefined,
      elementType: "pump",
      elementId: fertigationPump.id,
    });
  }

  // Add backwash device only for PL10 with fertigation backwash type
  if (
    project.backwash_pump_type === "FERTIGATION" &&
    pumpController.model === "WPC-PL10"
  ) {
    addBackwashDevice(
      deviceIdGen,
      devices,
      fertiMeshDevice,
      "2",
      fertigationPump.id
    );
  }
}

// ==============================================
// SERVICE DEVICE GENERATION
// ==============================================

/**
 * Add service devices to a mesh device
 *
 * @param deviceIdGen - ID generator for individual devices
 * @param devices - Array to add devices to
 * @param serviceMeshDevice - Service pump mesh device
 */
export function addServicePumpDevices(
  deviceIdGen: IdGenerator,
  devices: IrriganetDeviceWithElementId[],
  serviceMeshDevice: IrriganetMeshDeviceWithDeviceId,
  servicePump: WaterPumpWithController
): void {
  const pumpController = servicePump.water_pump_controller;

  if (pumpController.model === "WPC-PL10") {
    addServicePumpDevicesForPL10(
      deviceIdGen,
      devices,
      serviceMeshDevice,
      servicePump
    );
  } else {
    addServicePumpDevicesForPL50(
      deviceIdGen,
      devices,
      serviceMeshDevice,
      servicePump
    );
  }
}

/**
 * Add irrigation devices for WPC-PL10 controller
 */
function addServicePumpDevicesForPL10(
  deviceIdGen: IdGenerator,
  devices: IrriganetDeviceWithElementId[],
  individualMeshDevice: IrriganetMeshDeviceWithDeviceId,
  individualPump: WaterPumpWithController
): void {
  // Add irrigation pump device
  const irrigDeviceIdx = deviceIdGen.next();
  devices.push({
    idx: irrigDeviceIdx,
    mesh_idx: individualMeshDevice.idx,
    identity: "0",
    type: DEV_TYPES.ServicePump,
    out1: 1,
    out2: 0,
    input: individualMeshDevice.check_input ? 1 : 0,
    mode:
      (individualPump.mode === "PULSE" ? PULSE : 0) |
      (individualPump.monitor_operation ? MONI : 0),
    sector: undefined,
    ord_idx: irrigDeviceIdx - 1,
    eqpt_ver: undefined,
    power: undefined,
    elementType: "pump",
    elementId: individualPump.id,
  });
}

/**
 * Add irrigation devices for WPC-PL50 controller
 */
function addServicePumpDevicesForPL50(
  deviceIdGen: IdGenerator,
  devices: IrriganetDeviceWithElementId[],
  individualMeshDevice: IrriganetMeshDeviceWithDeviceId,
  individualPump: WaterPumpWithController
): void {
  const irrigDeviceIdx = deviceIdGen.next();
  devices.push({
    idx: irrigDeviceIdx,
    mesh_idx: individualMeshDevice.idx,
    identity: "0",
    type: DEV_TYPES.ServicePump,
    out1: 1,
    out2: individualPump.mode === "PULSE" ? 2 : 0,
    input: individualMeshDevice.check_input ? 1 : 0,
    mode:
      (individualPump.mode === "PULSE" ? PULSE : 0) |
      (individualPump.monitor_operation ? MONI : 0),
    sector: undefined,
    ord_idx: irrigDeviceIdx - 1,
    eqpt_ver: undefined,
    power: undefined,
    elementType: "pump",
    elementId: individualPump.id,
  });
}

// ==============================================
// VALVE DEVICE GENERATION
// ==============================================

/**
 * Add valve devices to a valve mesh device
 *
 * @param deviceIdGen - ID generator for individual devices
 * @param devices - Array to add devices to
 * @param meshDevice - Valve mesh device
 * @param project - Project data
 * @param sectors - Sectors controlled by this valve controller
 */
export function addValveDevices(
  deviceIdGen: IdGenerator,
  devices: IrriganetDeviceWithElementId[],
  meshDevice: IrriganetMeshDeviceWithDeviceId,
  project: ProjectWithSchedulingData,
  sectors: SectorWithValveController[]
): void {
  sectors.forEach((sector, index) => {
    const valveDeviceIdx = deviceIdGen.next();

    devices.push({
      idx: valveDeviceIdx,
      mesh_idx: meshDevice.idx,
      identity: index.toString(),
      type: DEV_TYPES.Valve,
      out1: 2 * index + 1,
      out2: 2 * index + 2,
      input: 0,
      mode: PULSE,
      sector: index + 1,
      elementType: "valve",
      elementId: sector.id,
      ord_idx: valveDeviceIdx - 1,
      eqpt_ver: undefined,
      power: project.irrigation_water_pump.has_frequency_inverter
        ? sector.power || 0
        : 0,
    });
  });
}

// ==============================================
// LEVEL DEVICE GENERATION
// ==============================================

/**
 * Add level devices to a level mesh device
 *
 * @param deviceIdGen - ID generator for individual devices
 * @param devices - Array to add devices to
 * @param meshDevice - Level mesh device
 
 */
export function addLevelDevices(
  deviceIdGen: IdGenerator,
  devices: IrriganetDeviceWithElementId[],
  meshDevice: IrriganetMeshDeviceWithDeviceId,
  _reservoir: ReservoirWithMonitorAndWaterPumpWithController
): void {
  const levelDeviceIdx = deviceIdGen.next();

  devices.push({
    idx: levelDeviceIdx,
    mesh_idx: meshDevice.idx,
    identity: "0",
    type: DEV_TYPES.Level,
    out1: 0,
    out2: 0,
    input: 0,
    mode: 0,
    sector: undefined,
    ord_idx: levelDeviceIdx - 1,
    eqpt_ver: undefined,
    power: undefined,
    elementType: "reservoir",
    elementId: _reservoir.id,
  });
}

// ==============================================
// SHARED DEVICE UTILITIES
// ==============================================

/**
 * Add a shared fertigation device (used when irrigation and fertigation controllers are shared)
 */
function addSharedFertigationDevice(
  deviceIdGen: IdGenerator,
  devices: IrriganetDeviceWithElementId[],
  meshDevice: IrriganetMeshDeviceWithDeviceId,
  waterPumpId: string
): void {
  const fertiDeviceIdx = deviceIdGen.next();
  devices.push({
    idx: fertiDeviceIdx,
    mesh_idx: meshDevice.idx,
    identity: "1",
    type: DEV_TYPES.Ferti,
    out1: 2,
    out2: 0,
    input: 0,
    mode: 0,
    sector: undefined,
    ord_idx: fertiDeviceIdx - 1,
    eqpt_ver: undefined,
    power: undefined,
    elementType: "pump",
    elementId: waterPumpId,
  });
}

/**
 * Add a backwash device
 */
function addBackwashDevice(
  deviceIdGen: IdGenerator,
  devices: IrriganetDeviceWithElementId[],
  meshDevice: IrriganetMeshDeviceWithDeviceId,
  identity: string,
  waterPumpId: string
): void {
  const backwashDeviceIdx = deviceIdGen.next();
  devices.push({
    idx: backwashDeviceIdx,
    mesh_idx: meshDevice.idx,
    identity,
    type: DEV_TYPES.Backwash,
    out1: 3,
    out2: 0,
    input: 0,
    mode: 0,
    sector: undefined,
    ord_idx: backwashDeviceIdx - 1,
    eqpt_ver: undefined,
    power: undefined,
    elementType: "pump",
    elementId: waterPumpId,
  });
}

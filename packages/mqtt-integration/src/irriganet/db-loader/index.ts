/**
 * Database Loaders for IrrigaNet System
 *
 * This module provides the main entry points for loading property state data
 * from the database and converting it into IrrigaNet format. The module has been
 * refactored to follow KISS, DRY, and Single Responsibility principles by
 * delegating specific responsibilities to focused sub-modules.
 */

import type { SQL } from "bun";
import DB from "../../db/connection";
import { createCodecConfig } from "./configuration";
import type { CodecConfig, LICState } from "./types";
import { generateMeshDevices } from "./device-generators";
import { generateScheduling } from "./scheduling-generators";
import {
  generateProjectGroups,
  loadProperty,
  loadPropertyLICs,
  loadTree,
} from "./tree-loader";
import { generateSectorsScheduling } from "./sector-scheduling-generator";
import { generateDevicesScheduling } from "./device-scheduling-generators";
import { getCurrentPropertyDeviceForDevice } from "../../db/queries/property-device-queries";

export { createCodecConfig } from "./configuration";

// ==============================================
// MAIN PUBLIC API
// ==============================================

/**
 * Load complete property state including all LIC devices and their configurations
 *
 * @param db - Database connection
 * @param propertyId - Property identifier
 * @param referenceDate - Reference date for active records
 * @returns Array of codec configurations with their associated collections
 */
export async function loadPropertyState(
  db: SQL,
  propertyId: string,
  referenceDate: Date
) {
  const result: Array<LICState> = [];

  const property = await loadProperty(db, propertyId);
  const propertyLICS = await loadPropertyLICs(db, propertyId, referenceDate);

  for (let idx = 0; idx < propertyLICS.length; idx++) {
    const lic = propertyLICS[idx]!;
    const codec: CodecConfig = createCodecConfig(idx + 1, lic, property);
    const state = await loadLICState(db, codec, referenceDate);
    result.push(state);
  }

  return result;
}

export async function loadLICStateByIdentifier(
  db: SQL,
  licIdentifier: string,
  referenceDate: Date,
  codecIdx = 1
): Promise<LICState | null> {
  const propertyDevice = await getCurrentPropertyDeviceForDevice(
    db,
    licIdentifier,
    "LIC",
    referenceDate
  );
  if (!propertyDevice) {
    //TODO
    //TODO
    return null;
  }
  const codec: CodecConfig = createCodecConfig(codecIdx, propertyDevice);
  const state = await loadLICState(db, codec, referenceDate);
  return state;
}

/**
 * Load LIC state including groups, devices, and mesh devices
 *
 * @param db - Database connection
 * @param config - Codec configuration
 * @param referenceDate - Reference date for processing
 * @returns Object containing groups, devices, and mesh devices
 */
export async function loadLICState(
  db: SQL,
  config: CodecConfig,
  referenceDate: Date
): Promise<LICState> {
  const tree = await loadTree(db, config.identity, referenceDate);
  const groups = generateProjectGroups(tree);
  const { devices, meshDevices } = generateMeshDevices(
    tree,
    groups,
    config,
    referenceDate
  );
  const schedules = generateScheduling(
    tree,
    groups,
    meshDevices,
    devices,
    referenceDate
  );
  const sectorSchedules = generateSectorsScheduling(
    tree,
    schedules,
    meshDevices,
    devices,
    referenceDate
  );
  const deviceSchedules = generateDevicesScheduling(schedules, sectorSchedules);

  return {
    lic: {
      ...config,
      irrigaMaisDeviceId: tree.id,
    },
    groups,
    devices,
    meshDevices,
    schedules,
    sectorSchedules,
    deviceSchedules,
  };
}

// ==============================================
// MAIN ENTRY POINT FOR TESTING
// ==============================================

if (import.meta.main) {
  loadPropertyState(DB, "0b22cc9d-cb5f-406f-8f75-4b960b359063", new Date())
    .then((config) => {
      console.log(JSON.stringify(config, null, 2));
    })
    .catch((error) => {
      console.error("Error building config:", error);
    });
}

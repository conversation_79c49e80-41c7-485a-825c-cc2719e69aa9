import type { SQL } from "bun";
import { codec } from "proto";
import type {
  CodecTransportStopCallback,
  ICodecTransport,
  ICodecTransportFactory,
} from "../transport/types";
import { loadLICStateByIdentifier } from "./db-loader";
import type { LICState } from "./db-loader/types";
import {
  automationPackage,
  commandPacket,
  configPackage,
  controlPackage,
  deviceSchedulingPackage,
  devicesPackage,
  firmware_updatePackage,
  request_infoPackage,
  schedulingPackage,
} from "./proto";
import { Logger, logger, LoggerManager } from "../log";
import { appendCRC16 } from "./db-loader/utilities";
import { getLIC } from "../db/queries/lic-queries";
import { insertLIC } from "../db/mutations/lic";
import db from "../db/connection";
import { insertCurrentLICPacket } from "../db/mutations/current-lic-packet";
import {
  markDeviceMessageRequestAsProcessing,
  markDeviceMessageRequestAsSent,
  markDeviceMessageRequestAsFailed,
} from "../db/mutations/device-message-request";
import fastq from "fastq";
import type { CurrentLICPacketInsert } from "../db/mutations/types";
import type { DeviceMessageRequestWithDevice } from "../db/queries/types";

const insertPacketQueue = fastq<unknown, CurrentLICPacketInsert, number>(
  async (task, cb) => {
    await insertCurrentLICPacket(db, task)
      .then((r) => {
        const err = r?.id
          ? null
          : new Error("Failed to insert current LIC packet");
        cb(err, r?.id);
      })
      .catch((err) => {
        cb(err);
      });
  },
  1
); // Limit concurrency to 1

function isJSONPayload(payload: Buffer): boolean {
  return payload.at(0) === 123 && payload.at(-1) === 125; // Check if payload starts with '{' and ends with '}'
}

const parseMessageLogger = LoggerManager.getLogger("CodecManager.parseMessage");
function parseMessage(topic: string, message: Buffer, referenceDate: Date) {
  if (isJSONPayload(message)) {
    try {
      const json_payload = JSON.parse(message.toString());
      return { json_payload: json_payload };
    } catch (error) {
      parseMessageLogger.error("Failed to decode JSON report message:", error);
    }
    return null;
  }
  try {
    if (topic === "report") {
      const decoded = codec.out.OutgoingPacket.decode(message);
      if (parseMessageLogger.traceEnabled) {
        parseMessageLogger.debug(
          "Decoded report message:",
          `payload=${decoded.payload}`,
          JSON.stringify(decoded, null, 2)
        );
      }
      return decoded;
    }
  } catch (error) {
    parseMessageLogger.error("Failed to decode report message:", error);
  }
  return null;
}
// "config" | "devices" | "scheduling" | "dev_scheduling" | "automation" | "control" | "command" | "request_info" | "firmware_update"
const packetBuilders = {
  config: configPackage,
  devices: devicesPackage,
  scheduling: schedulingPackage,
  dev_scheduling: deviceSchedulingPackage,
  automation: automationPackage,
  request_info: request_infoPackage,
  firmware_update: firmware_updatePackage,
  control: controlPackage,
  command: commandPacket,
} as const;

type PacketBuilders = typeof packetBuilders;
type Tail<T extends any[]> = T extends [any, ...infer R] ? R : never;
type TailArgs<F extends (...a: any[]) => any> = Tail<Parameters<F>>;
type FirstArg<F extends (...a: any[]) => any> = Parameters<F>[0];
type FullArgs<K extends keyof PacketBuilders> = Parameters<PacketBuilders[K]>;
type IncomingPacketPayloadType = NonNullable<
  codec.in_.IncomingPacket["payload"]
>;

const log = LoggerManager.getLogger("CodecManager");
export class CodecManager {
  private readonly stopCallback: CodecTransportStopCallback;
  constructor(
    private licIdentifier: string,
    private referenceDate: Date,
    private state: LICState,
    private transport: ICodecTransport
  ) {
    this.stopCallback = this.transport.onMessage(
      async (topic, payload, referenceDate) => {
        await this.handleMessage(topic, payload, referenceDate);
      }
    );
  }

  getState(): LICState {
    return this.state;
  }

  getLicIdentifier() {
    return this.licIdentifier;
  }

  getReferenceDate() {
    return this.referenceDate;
  }

  async handleMessage(topic: string, message: Buffer, referenceDate: Date) {
    // Handle the incoming message

    log.debug(
      `[${this.licIdentifier}] Received report message on topic ${topic}:`,
      message
    );
    const parsed = parseMessage(topic, message, referenceDate);
    if (parsed) {
      const payloadType =
        "json_payload" in parsed ? "json_payload" : parsed.payload;
      const payloadData =
        "json_payload" in parsed ? parsed.json_payload : parsed.toJSON();
      if (log.traceEnabled) {
        log.debug(
          `[${this.licIdentifier}] Parsed message ${payloadType} on topic ${topic}:`,
          payloadData
        );
      }
      insertPacketQueue.push(
        {
          device: this.state.lic.irrigaMaisDeviceId,
          packet_date: referenceDate,
          payload_type: payloadType ?? "unknown",
          payload_data: payloadData,
        },
        (err, r) => {
          if (err) {
            log.error(
              `[${this.licIdentifier}] Failed to insert LIC packet for device ${this.state.lic.irrigaMaisDeviceId}:`,
              err
            );
          } else {
            log.debug(
              `[${this.licIdentifier}] Inserted LIC packet for device ${this.state.lic.irrigaMaisDeviceId} with id=${r}`
            );
          }
        }
      );
    } else {
      log.error(
        `[${this.licIdentifier}] Failed to parse message on topic ${topic}:`,
        message
      );
    }
  }

  send<K extends IncomingPacketPayloadType>(
    messageType: K,
    id: Date | number,
    ...params: TailArgs<PacketBuilders[K]>
  ) {
    id = typeof id === "number" ? id : Math.round(id.getTime() / 1000);
    const packet = this.createIncomingPacket(messageType, id, ...params);
    this.sendPacket(packet);
  }

  /**
   * Send a message based on device_message_request data with database integration.
   * This method handles the complete lifecycle of sending a message:
   * 1. Mark message as processing and increment attempts
   * 2. Build and send the protobuf message
   * 3. Update status based on send result
   *
   * @param messageRequest - Device message request with device info
   * @returns Promise that resolves to success/failure status
   */
  async sendDeviceMessageRequest(
    messageRequest: DeviceMessageRequestWithDevice
  ): Promise<{ success: boolean; error?: string }> {
    const messageId = messageRequest.id;
    const messageType: IncomingPacketPayloadType = messageRequest.payload_type;
    const packetId = Number(messageRequest.packet_id);
    const payloadData = messageRequest.payload_data;
    log.info(
      `[${
        this.licIdentifier
      }] Handling device message request ${messageId} (${messageType}, packet_id: ${packetId}), payload_data: ${JSON.stringify(
        payloadData
      )}`
    );
    try {
      // Step 1: Create the protobuf packet
      const packet = this.createIncomingPacket(
        messageType,
        packetId,
        payloadData
      );
      const payloadBytes = Buffer.from(
        appendCRC16(codec.in_.IncomingPacket.encode(packet).finish())
      );

      // Step 2: Mark message as processing and store payload_bytes
      log.debug(
        `[${this.licIdentifier}] Marking packet_id=${messageId} as processing`
      );
      const processingUpdate = await markDeviceMessageRequestAsProcessing(
        db,
        messageId,
        payloadBytes
      );

      if (!processingUpdate) {
        const error =
          "Failed to mark message as processing - message may have been processed already";
        log.warn(`[${this.licIdentifier}] ${error} for message ${messageId}`);
        return { success: false, error };
      }

      log.debug(
        `[${this.licIdentifier}] Marked message ${messageId} as processing (attempt ${processingUpdate.attempts})`
      );

      // Step 3: Send the message via transport
      try {
        log.info(
          `[${this.licIdentifier}] Sending message ${messageId} (${messageType}, packet_id: ${packetId})`
        );
        await this.transport.sendMessage(payloadBytes);

        // Step 4: Mark as sent on successful transmission
        log.debug(
          `[${this.licIdentifier}] Marking packet_id=${messageId} as sent`
        );
        const sentUpdate = await markDeviceMessageRequestAsSent(db, messageId);

        if (sentUpdate) {
          log.info(
            `[${this.licIdentifier}] Successfully sent message ${messageId} (${messageType}, packet_id: ${packetId})`
          );
          return { success: true };
        } else {
          const error = "Failed to mark message as sent in database";
          log.error(
            `[${this.licIdentifier}] ${error} for message ${messageId}`
          );
          return { success: false, error };
        }
      } catch (sendError) {
        // Step 4b: Mark as failed on transmission error
        log.debug(
          `[${this.licIdentifier}] Marking packet_id=${messageId} as failed`
        );
        const errorMessage =
          sendError instanceof Error ? sendError.message : String(sendError);

        await markDeviceMessageRequestAsFailed(db, messageId, errorMessage);

        log.error(
          `[${this.licIdentifier}] Failed to send message ${messageId}: ${errorMessage}`
        );

        return { success: false, error: errorMessage };
      }
    } catch (error) {
      // Handle any other errors (packet creation, database errors, etc.)
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      try {
        await markDeviceMessageRequestAsFailed(db, messageId, errorMessage);
      } catch (dbError) {
        log.error(
          `[${this.licIdentifier}] Failed to mark message ${messageId} as failed: ${dbError}`
        );
      }

      log.error(
        `[${this.licIdentifier}] Error processing message ${messageId}: ${errorMessage}`
      );

      return { success: false, error: errorMessage };
    }
  }

  private sendPacket(packet: codec.in_.IIncomingPacket) {
    const payload = appendCRC16(
      codec.in_.IncomingPacket.encode(packet).finish()
    );
    this.transport.sendMessage(Buffer.from(payload));
  }

  async reloadFromDB(db: SQL, referenceDate: Date) {
    this.referenceDate = referenceDate;
    await loadLICStateByIdentifier(db, this.licIdentifier, referenceDate).then(
      (state) => {
        if (!state) {
          log.error(`[${this.licIdentifier}] Failed to load LIC state`);
          return null;
        }
        this.state = state;
      }
    );
  }

  private createIncomingPacket<K extends keyof PacketBuilders>(
    messageType: K,
    id: number,
    ...params: TailArgs<PacketBuilders[K]>
  ): codec.in_.IIncomingPacket {
    const fn = packetBuilders[messageType] as unknown as (
      ...a: FullArgs<K>
    ) => ReturnType<PacketBuilders[K]>;

    const args = [this.state, ...params] as unknown as FullArgs<K>;

    const innerData = fn(...args);

    return {
      id,
      [messageType]: innerData,
    } as codec.in_.IIncomingPacket;
  }

  /**
   * Create an incoming packet from device_message_request payload data.
   * This method reconstructs the packet using the stored payload_data.
   *
   * @param messageType - Type of protobuf payload
   * @param id - Packet ID
   * @param payloadData - JSON payload data from device_message_request
   * @returns Protobuf IncomingPacket
   */
  private createIncomingPacketFromRequest<K extends keyof PacketBuilders>(
    messageType: K,
    id: number,
    payloadData: Record<string, any>
  ): codec.in_.IIncomingPacket {
    // For device_message_request, the payload_data should contain the already-built
    // message data that can be directly used as the inner payload
    return {
      id,
      [messageType]: payloadData,
    } as codec.in_.IIncomingPacket;
  }

  static async loadFromDB(
    db: SQL,
    licIdentifier: string,
    transportFactory: ICodecTransportFactory,
    referenceDate: Date
  ) {
    let state = await loadLICStateByIdentifier(
      db,
      licIdentifier,
      referenceDate
    );
    if (!state) {
      let lic = await getLIC(db, licIdentifier);
      if (!lic) {
        lic = await insertLIC(db, licIdentifier);
        if (!lic) {
          logger.error(`[${licIdentifier}] Failed to create LIC`);
          return null;
        }
      }
      state = {
        devices: [],
        deviceSchedules: [],
        groups: [],
        meshDevices: [],
        schedules: [],
        sectorSchedules: [],
        lic: {
          irrigaMaisDeviceId: lic.id,
          enabled: 1,
          identity: lic.identifier,
          idx: 1,
          name: lic.identifier,
          propertyDeviceId: "", // TODO: make propertyDeviceId optional
          config: {},
        },
      };
    }
    return new CodecManager(
      licIdentifier,
      referenceDate,
      state,
      transportFactory.createTransport(licIdentifier)
    );
  }
}

import { codec } from "proto";
import { logger } from "./log";
import type { MQTTTopicMessage, MQTTTopicType } from "./types";
import fs from "node:fs";
import { crc16 } from "./irriganet/db-loader/utilities";

export function handleMQTTTopicMessage(
  message: MQTTTopicMessage<MQTTTopicType>
) {
  if (message.topicType === "report") {
    logger.log(
      `Handling report for device ${message.deviceId}:`,
      message.payload.toString("hex")
    );
    // Process report message
    return handleReportMessage(message);
  } else if (message.topicType === "downlink") {
    logger.log(
      `Handling downlink for device ${message.deviceId}:`,
      message.payload.toString()
    );
    // Process downlink message
    return handleDownlinkMessage(message);
  } else if (message.topicType === "unknown_codec_topic") {
    logger.warn(
      `Received unknown codec topic for device ${message.deviceId}:`,
      message.topic
    );
    // Handle unknown codec topic
    saveMessage(message, message);
  } else {
    logger.error(`Unknown topic type: ${JSON.stringify(message)}`);
  }
}

function isJSONPayload(payload: Buffer): boolean {
  return payload.at(0) === 123 && payload.at(-1) === 125; // Check if payload starts with '{' and ends with '}'
}

function handleReportMessage(message: MQTTTopicMessage<"report">) {
  if (isJSONPayload(message.payload)) {
    try {
      const jsonPayload = JSON.parse(message.payload.toString());
      logger.log(
        "Decoded JSON report message:",
        JSON.stringify(jsonPayload, null, 2)
      );
      saveMessage(message, { jsonPayload });
    } catch (error) {
      logger.error("Failed to decode JSON report message:", error);
    }
    return;
  }
  try {
    const decoded = codec.out.OutgoingPacket.decode(message.payload);
    logger.log(
      "Decoded report message:",
      `payload=${decoded.payload}`,
      JSON.stringify(decoded, null, 2)
    );
    saveMessage(message, decoded.toJSON(), decoded.payload);
  } catch (error) {
    logger.error("Failed to decode report message:", error);
  }
}

function handleDownlinkMessage(message: MQTTTopicMessage<"downlink">) {
  // Implement your logic to handle downlink messages
  logger.log("Handling downlink message:", message);
  try {
    const payload = message.payload.subarray(0, message.payload.length - 2);
    const crc = message.payload.readUInt16BE(message.payload.length - 2);
    const calculatedCRC = crc16(payload);
    if (crc !== calculatedCRC) {
      logger.error(
        `CRC mismatch for downlink message ${message.deviceId}: expected ${calculatedCRC}, got ${crc}`
      );
      return;
    }
    const decoded = codec.in_.IncomingPacket.decode(payload);
    logger.log(
      "Decoded downlink message:",
      JSON.stringify(decoded.toJSON(), null, 2)
    );
    saveMessage(message, decoded.toJSON(), decoded.payload);
  } catch (error) {
    logger.error("Failed to decode downlink message:", error);
  }
}

// const data = Buffer.from("08a1f89ec406220e0a0c10d388fec306200f40015801", "hex");

// const decoded = codec.out.OutgoingPacket.decode(data);

// logger.log(decoded.toJSON());

function saveMessage(
  message: MQTTTopicMessage<MQTTTopicType>,
  decodedPayload: unknown,
  subFolder = ""
) {
  // Implement your logic to save the message
  logger.log("Saving message:", message);
  const outputDir = `./.local/messages/${message.deviceId}/${
    message.topicType
  }${subFolder ? `/${subFolder}` : ""}`;
  // Create the directory if it doesn't exist
  fs.mkdirSync(outputDir, { recursive: true });
  // Save the message to a file
  fs.writeFileSync(
    `${outputDir}/${Date.now()}.json`,
    JSON.stringify(decodedPayload, null, 2)
  );
}

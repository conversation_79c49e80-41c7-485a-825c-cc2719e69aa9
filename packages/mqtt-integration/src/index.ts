import db from "./db/connection";
import { CodecManagerRegistry } from "./irriganet/codec-manager-registry";
import { DeviceMessageQueueService } from "./db/services/device-message-queue";
import { logger } from "./log";
import { createMQTTClient } from "./transport/mqtt/mqtt-client";
import { MQTTTransportFactory } from "./transport/mqtt/mqtt-transport-factory";

const client = createMQTTClient();

const codecStateRegistry = new CodecManagerRegistry();
const transportFactory = new MQTTTransportFactory(
  client,
  (licIdentifier, topic, message, referenceDate) => {
    logger.log(`Unhandled message for ${licIdentifier} on ${topic}:`, message);
    codecStateRegistry
      .get(db, licIdentifier, transportFactory, referenceDate)
      .then((manager) => {
        // Handle the codec state manager
        manager.handleMessage(topic, message, referenceDate);
      });
  }
);

// Initialize CodecManagerRegistry
codecStateRegistry.init(db, transportFactory, new Date());

// Create and configure DeviceMessageQueueService
const deviceMessageQueueService = new DeviceMessageQueueService(db, {
  pollingIntervalMs: 500, // 5 seconds
  batchSize: 50,
  retryBatchSize: 20,
  enableRetryProcessing: true,
  autoStart: false, // We'll start it manually after wiring events
});

// Wire event listeners between DeviceMessageQueueService and CodecManagerRegistry
deviceMessageQueueService.on(
  "device_message_request",
  async (messageRequest) => {
    await codecStateRegistry.handleDeviceMessageRequest(messageRequest);
  }
);

deviceMessageQueueService.on("device_message_retry", async (messageRequest) => {
  await codecStateRegistry.handleDeviceMessageRetry(messageRequest);
});

// Optional: Add logging for service events
deviceMessageQueueService.on("service_started", () => {
  logger.log("DeviceMessageQueueService started");
});

deviceMessageQueueService.on("service_stopped", () => {
  logger.log("DeviceMessageQueueService stopped");
});

deviceMessageQueueService.on(
  "polling_cycle_complete",
  (processedCount, retryCount) => {
    if (processedCount > 0 || retryCount > 0) {
      logger.log(
        `DeviceMessageQueueService cycle: processed=${processedCount}, retry=${retryCount}`
      );
    }
  }
);

deviceMessageQueueService.on("polling_error", (error) => {
  logger.error("DeviceMessageQueueService polling error:", error);
});

// Start the DeviceMessageQueueService
deviceMessageQueueService.start();

// client.subscribe("#", (err) => {
//   if (err) {
//     console.error("Failed to subscribe to topics:", err);
//   } else {
//     logger.log("Subscribed to all topics");
//   }
// });

// Handle graceful shutdown
const shutdown = () => {
  logger.log("Shutting down services...");

  // Stop DeviceMessageQueueService
  deviceMessageQueueService.destroy();

  // Disconnect from MQTT broker
  client.end(() => {
    logger.log("Disconnected from MQTT broker");
    process.exit(0);
  });
};

process.on("SIGINT", shutdown);
process.on("SIGTERM", shutdown);
process.on("exit", shutdown);

import { codec } from "proto";
import { appendCRC16 } from "./irriganet/db-loader/utilities";
import { createMQTTClient } from "./transport/mqtt/mqtt-client";

const codecGeoId = "ECC9FF468E64";
const codecId = "ECC9FF468EB0";

const pkg = {
  id: Math.round(Date.now() / 1000),
  request_info: {
    type: 0,
  },
};
const data = Buffer.from(
  appendCRC16(codec.in_.IncomingPacket.encode(pkg).finish())
);

const client = createMQTTClient();
client.on("connect", () => {
  client.publish(`/codec/${codecId}/downlink`, data, (err, packet) => {
    if (err) {
      console.error("Error publishing MQTT message:", err);
    } else {
      console.log("Published MQTT message:", packet);
    }
    client.end();
  });
});

// const data = Buffer.from([
//   0x08, 0xe4, 0xfa, 0xa2, 0xc5, 0x06, 0x4a, 0x00, 0x8d, 0xe3,
// ]);

// const crc = crc16(data.subarray(0, data.length - 2)); // Calculate CRC for the data excluding the last 2 bytes
// const crcBytes = Buffer.from([
//   (crc >> 8) & 0xff, // High byte
//   crc & 0xff, // Low byte
// ]);

// console.log(crc, crcBytes, data.readUInt16BE(data.length - 2));

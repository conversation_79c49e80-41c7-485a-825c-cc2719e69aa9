import type { SQL } from "bun";
import type { CurrentLICPacketInsert } from "./types";
import type { CurrentLICPacket } from "../queries/types";

export async function insertCurrentLICPacket(
  db: SQL,
  record: CurrentLICPacketInsert
) {
  const [r] = await db<
    CurrentLICPacket[]
  >`INSERT INTO current_lic_packet (device, packet_date, payload_type, payload_data) VALUES (
        ${record.device},
        ${record.packet_date},
        ${record.payload_type},
        ${record.payload_data}
    ) RETURNING *`;
  return r;
}

import type { CurrentLICPacket, DeviceMessageRequest } from "../queries/types";

export type CurrentLICPacketInsert = Omit<
  CurrentLICPacket,
  "id" | "date_created"
>;

export type DeviceMessageRequestInsert = Omit<
  DeviceMessageRequest,
  "id" | "date_created" | "date_updated" | "user_created" | "user_updated"
>;

export type DeviceMessageRequestUpdate = Partial<
  Pick<
    DeviceMessageRequest,
    | "status"
    | "attempts"
    | "payload_bytes"
    | "sent_at"
    | "acknowledged_at"
    | "last_error"
    | "metadata"
    | "notes"
  >
>;

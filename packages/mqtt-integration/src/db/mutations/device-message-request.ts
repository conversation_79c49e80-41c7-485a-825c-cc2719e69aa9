import type { SQL } from "bun";
import { directusClient } from "../../directus";
import type { DeviceMessageRequest } from "../queries/types";

function updateDirectusCache() {
  directusClient.clearCache();
  return Promise.resolve();
}

/**
 * Update device message request status to "processing" and increment attempts.
 * This is called before sending a message to mark it as being processed.
 *
 * @param db - Database connection
 * @param messageId - Message UUID
 * @param payloadBytes - Compiled protobuf binary data
 * @returns Updated device message request or null if not found
 */
export async function markDeviceMessageRequestAsProcessing(
  db: SQL,
  messageId: string,
  payloadBytes: Buffer
): Promise<DeviceMessageRequest | null> {
  const [updated] = await db<DeviceMessageRequest[]>`
    UPDATE device_message_request 
    SET 
      status = 'processing',
      attempts = attempts + 1,
      payload_bytes = ${payloadBytes},
      date_updated = CURRENT_TIMESTAMP
    WHERE id = ${messageId}
    AND status IN ('pending', 'failed')  -- Only allow transition from pending or failed
    RETURNING *
  `;
  await updateDirectusCache();
  return updated || null;
}

/**
 * Update device message request status to "sent" after successful transmission.
 *
 * @param db - Database connection
 * @param messageId - Message UUID
 * @returns Updated device message request or null if not found
 */
export async function markDeviceMessageRequestAsSent(
  db: SQL,
  messageId: string
): Promise<DeviceMessageRequest | null> {
  const [updated] = await db<DeviceMessageRequest[]>`
    UPDATE device_message_request 
    SET 
      status = 'sent',
      sent_at = CURRENT_TIMESTAMP,
      acknowledged_at = NULL,
      last_error = NULL,
      date_updated = CURRENT_TIMESTAMP
    WHERE id = ${messageId}
    AND status = 'processing'  -- Only allow transition from processing
    RETURNING *
  `;
  await updateDirectusCache();
  return updated || null;
}

/**
 * Update device message request status to "failed" after transmission failure.
 *
 * @param db - Database connection
 * @param messageId - Message UUID
 * @param errorMessage - Error message to store
 * @returns Updated device message request or null if not found
 */
export async function markDeviceMessageRequestAsFailed(
  db: SQL,
  messageId: string,
  errorMessage: string
): Promise<DeviceMessageRequest | null> {
  const [updated] = await db<DeviceMessageRequest[]>`
    UPDATE device_message_request 
    SET 
      status = 'failed',
      last_error = ${errorMessage},
      date_updated = CURRENT_TIMESTAMP
    WHERE id = ${messageId}
    AND status = 'processing'  -- Only allow transition from processing
    RETURNING *
  `;
  await updateDirectusCache();
  return updated || null;
}

/**
 * Update device message request status to "acknowledged" when device confirms receipt.
 *
 * @param db - Database connection
 * @param messageId - Message UUID
 * @returns Updated device message request or null if not found
 */
export async function markDeviceMessageRequestAsAcknowledged(
  db: SQL,
  messageId: string
): Promise<DeviceMessageRequest | null> {
  const [updated] = await db<DeviceMessageRequest[]>`
    UPDATE device_message_request 
    SET 
      status = 'acknowledged',
      acknowledged_at = CURRENT_TIMESTAMP,
      date_updated = CURRENT_TIMESTAMP
    WHERE id = ${messageId}
    AND status = 'sent'  -- Only allow transition from sent
    RETURNING *
  `;
  await updateDirectusCache();
  return updated || null;
}

/**
 * Update device message request status to "expired" for messages that have exceeded their expiry time.
 *
 * @param db - Database connection
 * @param messageId - Message UUID
 * @returns Updated device message request or null if not found
 */
export async function markDeviceMessageRequestAsExpired(
  db: SQL,
  messageId: string
): Promise<DeviceMessageRequest | null> {
  const [updated] = await db<DeviceMessageRequest[]>`
    UPDATE device_message_request 
    SET 
      status = 'expired',
      date_updated = CURRENT_TIMESTAMP
    WHERE id = ${messageId}
    AND status IN ('pending', 'processing', 'failed')  -- Can expire from these states
    AND expires_at IS NOT NULL 
    AND expires_at <= CURRENT_TIMESTAMP
    RETURNING *
  `;
  await updateDirectusCache();
  return updated || null;
}

/**
 * Update device message request status to "cancelled".
 *
 * @param db - Database connection
 * @param messageId - Message UUID
 * @returns Updated device message request or null if not found
 */
export async function markDeviceMessageRequestAsCancelled(
  db: SQL,
  messageId: string
): Promise<DeviceMessageRequest | null> {
  const [updated] = await db<DeviceMessageRequest[]>`
    UPDATE device_message_request 
    SET 
      status = 'cancelled',
      date_updated = CURRENT_TIMESTAMP
    WHERE id = ${messageId}
    AND status IN ('pending', 'processing', 'failed')  -- Can cancel from these states
    RETURNING *
  `;
  await updateDirectusCache();
  return updated || null;
}

/**
 * Bulk update to mark expired messages as expired.
 * This is useful for cleanup operations.
 *
 * @param db - Database connection
 * @param referenceDate - Current timestamp for expiry comparison
 * @returns Number of messages marked as expired
 */
export async function markExpiredDeviceMessageRequests(
  db: SQL,
  referenceDate: Date = new Date()
): Promise<number> {
  const result = await db`
    UPDATE device_message_request 
    SET 
      status = 'expired',
      date_updated = CURRENT_TIMESTAMP
    WHERE status IN ('pending', 'processing', 'failed')
    AND expires_at IS NOT NULL 
    AND expires_at <= ${referenceDate}
  `;
  await updateDirectusCache();
  return result.count || 0;
}

/**
 * Reset a failed message back to pending status for retry.
 * This is useful for manual retry operations or when retry conditions are met.
 *
 * @param db - Database connection
 * @param messageId - Message UUID
 * @returns Updated device message request or null if not found
 */
export async function resetDeviceMessageRequestToPending(
  db: SQL,
  messageId: string
): Promise<DeviceMessageRequest | null> {
  const [updated] = await db<DeviceMessageRequest[]>`
    UPDATE device_message_request 
    SET 
      status = 'pending',
      last_error = NULL,
      date_updated = CURRENT_TIMESTAMP
    WHERE id = ${messageId}
    AND status = 'failed'  -- Only allow reset from failed status
    AND attempts < max_attempts  -- Only if attempts haven't been exceeded
    RETURNING *
  `;
  await updateDirectusCache();
  return updated || null;
}

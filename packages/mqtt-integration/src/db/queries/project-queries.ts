import type { SQL } from "bun";
import type { Project, ProjectFull } from "./types";

/**
 * Returns all projects whose `localized_irrigation_controller` has the LIC identifier
 * and is active at the reference date
 * and belongs to to a property that which has a property_device record for the device identifier and is active at the reference date
 * ordered by creation date.
 * @param db
 * @param licIdentifier
 * @param referenceDate
 * @returns
 */
export async function listProjectsByLICIdentifier(
  db: SQL,
  licIdentifier: string,
  referenceDate: Date = new Date()
): Promise<Project[]> {
  return await db<Project[]>`
    SELECT p.*
    FROM project p
    JOIN property_device pd ON p.property = pd.property
    JOIN device d ON pd.device = d.id
    WHERE d.identifier = ${licIdentifier}
      AND d.model = 'LIC'
      AND ${referenceDate} >= COALESCE(pd.start_date, '-infinity'::timestamp)
      AND ${referenceDate} <= COALESCE(pd.end_date, 'infinity'::timestamp)
      AND p.localized_irrigation_controller = d.id
      AND ${referenceDate} >= COALESCE(p.start_date, '-infinity'::timestamp)
      AND ${referenceDate} <= COALESCE(p.end_date, 'infinity'::timestamp)
    ORDER BY p.date_created ASC
  `;
}

/**
 * Returns all projects tree (full projects) whose `localized_irrigation_controller` has the LIC identifier
 * and is active at the reference date
 * and belongs to to a property that which has a property_device record for the device identifier and is active at the reference date
 * ordered by creation date.
 * @param db
 * @param licIdentifier
 * @param referenceDate
 * @returns
 */
export async function listFullProjectsByLICIdentifier(
  db: SQL,
  licIdentifier: string,
  referenceDate: Date = new Date()
): Promise<ProjectFull[]> {
  return await db<ProjectFull[]>`
    /*
      Retrieve full project trees for projects whose localized irrigation controller (LIC)
      matches the provided identifier and is active at the reference date.
      This variant uses subqueries in the SELECT clause (instead of many JOINs)
      to build the JSON objects for related entities. Helpful comments are inline.
    */

    SELECT
      p.*,

      -- Localized Irrigation Controller (LIC) as a single JSON object
      ( SELECT
          jsonb_build_object(
            'id', d.id,
            'identifier', d.identifier,
            'model', d.model,
            'date_created', d.date_created,
            'user_created', d.user_created,
            'date_updated', d.date_updated,
            'user_updated', d.user_updated,
            'metadata', d.metadata, -- Not used for LIC WIFI config/label (see note)
            'notes', d.notes
          )
        FROM property_device pd
        JOIN device d ON pd.device = d.id
        -- match property and LIC identifier, ensure device model and property_device active at reference date
        WHERE pd.property = p.property
          AND d.identifier = ${licIdentifier}
          AND d.model = 'LIC'
          AND ${referenceDate} >= COALESCE(pd.start_date, '-infinity'::timestamp)
          AND ${referenceDate} <= COALESCE(pd.end_date, 'infinity'::timestamp)
        LIMIT 1
      ) AS localized_irrigation_controller,

      -- Irrigation Water Pump (iwp) with its controller embedded
      ( SELECT
          CASE WHEN iwp.id IS NOT NULL THEN
            jsonb_build_object(
              'id', iwp.id,
              'property', iwp.property,
              'water_pump_controller', iwp.water_pump_controller,
              'label', iwp.label,
              'identifier', iwp.identifier,
              'pump_type', iwp.pump_type,
              'pump_model', iwp.pump_model,
              'has_frequency_inverter', iwp.has_frequency_inverter,
              'monitor_operation', iwp.monitor_operation,
              'flow_rate_lh', iwp.flow_rate_lh,
              'mode', iwp.mode,
              'notes', iwp.notes,
              'metadata', iwp.metadata,
              'date_created', iwp.date_created,
              'user_created', iwp.user_created,
              'date_updated', iwp.date_updated,
              'user_updated', iwp.user_updated,
              'controller', jsonb_build_object(
                'id', ctrl.id,
                'identifier', ctrl.identifier,
                'model', ctrl.model,
                'date_created', ctrl.date_created,
                'user_created', ctrl.user_created,
                'date_updated', ctrl.date_updated,
                'user_updated', ctrl.user_updated,
                'metadata', ctrl.metadata,
                'notes', ctrl.notes
              )
            )
          ELSE NULL END
        FROM water_pump iwp
        LEFT JOIN device ctrl ON iwp.water_pump_controller = ctrl.id
          AND ctrl.model IN ('WPC-PL10', 'WPC-PL50') -- only include these controller models
        WHERE iwp.id = p.irrigation_water_pump
        LIMIT 1
      ) AS irrigation_water_pump,

      -- Fertigation Water Pump (fwp) with its controller embedded
      ( SELECT
          CASE WHEN fwp.id IS NOT NULL THEN
            jsonb_build_object(
              'id', fwp.id,
              'property', fwp.property,
              'water_pump_controller', fwp.water_pump_controller,
              'label', fwp.label,
              'identifier', fwp.identifier,
              'pump_type', fwp.pump_type,
              'pump_model', fwp.pump_model,
              'has_frequency_inverter', fwp.has_frequency_inverter,
              'monitor_operation', fwp.monitor_operation,
              'flow_rate_lh', fwp.flow_rate_lh,
              'mode', fwp.mode,
              'notes', fwp.notes,
              'metadata', fwp.metadata,
              'date_created', fwp.date_created,
              'user_created', fwp.user_created,
              'date_updated', fwp.date_updated,
              'user_updated', fwp.user_updated,
              'controller', jsonb_build_object(
                'id', ctrl2.id,
                'identifier', ctrl2.identifier,
                'model', ctrl2.model,
                'date_created', ctrl2.date_created,
                'user_created', ctrl2.user_created,
                'date_updated', ctrl2.date_updated,
                'user_updated', ctrl2.user_updated,
                'metadata', ctrl2.metadata,
                'notes', ctrl2.notes
              )
            )
          ELSE NULL END
        FROM water_pump fwp
        LEFT JOIN device ctrl2 ON fwp.water_pump_controller = ctrl2.id
          AND ctrl2.model IN ('WPC-PL10', 'WPC-PL50')
        WHERE fwp.id = p.fertigation_water_pump
        LIMIT 1
      ) AS fertigation_water_pump,

      -- Sectors array: aggregate sectors for the project, each with its valve_controller_device (if any)
      ( SELECT
          COALESCE(
            jsonb_agg(
              DISTINCT jsonb_build_object(
                'id', s.id,
                'project', s.project,
                'name', s.name,
                'valve_controller', s.valve_controller,
                'valve_controller_output', s.valve_controller_output,
                'power', s.power,
                'description', s.description,
                'date_created', s.date_created,
                'user_created', s.user_created,
                'date_updated', s.date_updated,
                'user_updated', s.user_updated,
                'metadata', s.metadata,
                'notes', s.notes,
                'valve_controller_device', jsonb_build_object(
                  'id', vc.id,
                  'identifier', vc.identifier,
                  'model', vc.model,
                  'date_created', vc.date_created,
                  'user_created', vc.user_created,
                  'date_updated', vc.date_updated,
                  'user_updated', vc.user_updated,
                  'metadata', vc.metadata,
                  'notes', vc.notes
                )
              )
            ) FILTER (WHERE s.id IS NOT NULL),
            '[]'::jsonb
          )
        FROM sector s
        LEFT JOIN property_device vc_pd ON s.valve_controller = vc_pd.device
          AND vc_pd.property = p.property
          AND ${referenceDate} >= COALESCE(vc_pd.start_date, '-infinity'::timestamp)
          AND ${referenceDate} <= COALESCE(vc_pd.end_date, 'infinity'::timestamp)
        LEFT JOIN device vc ON vc_pd.device = vc.id
          AND vc.model = 'VC'
        WHERE s.project = p.id
        ORDER BY s.name ASC
      ) AS sectors

    FROM project p

    /*
      Ensure the project is associated with the requested LIC (active at reference date)
      and the project's localized_irrigation_controller points to that LIC device.
      Using EXISTS to replicate the original join/filter logic.
    */
    WHERE EXISTS (
      SELECT 1
      FROM property_device pd2
      JOIN device d2 ON pd2.device = d2.id
      WHERE pd2.property = p.property
        AND d2.identifier = ${licIdentifier}
        AND d2.model = 'LIC'
        AND ${referenceDate} >= COALESCE(pd2.start_date, '-infinity'::timestamp)
        AND ${referenceDate} <= COALESCE(pd2.end_date, 'infinity'::timestamp)
        -- ensure the project's localized_irrigation_controller references this device
        AND p.localized_irrigation_controller = d2.id
    )
      -- project must be active at reference date
      AND ${referenceDate} >= COALESCE(p.start_date, '-infinity'::timestamp)
      AND ${referenceDate} <= COALESCE(p.end_date, 'infinity'::timestamp)

    ORDER BY p.date_created ASC
  `;
}

import type { <PERSON><PERSON> } from "bun";
import type { ReservoirWithMonitorAndWaterPumpWithController } from "./types";

export function listReservoirsWithMonitorAndWaterPumpByLICIdentifier(
  db: SQL,
  licIdentifier: string,
  referenceDate: Date = new Date()
): Promise<ReservoirWithMonitorAndWaterPumpWithController[]> {
  return db<ReservoirWithMonitorAndWaterPumpWithController[]>`
    select
      reservoir.*,
      jsonb_build_object(
        'id', mesh_device_monitor.id,
        'identifier', mesh_device_monitor.identifier,
        'model', mesh_device_monitor.model,
        'date_created', mesh_device_monitor.date_created,
        'user_created', mesh_device_monitor.user_created,
        'date_updated', mesh_device_monitor.date_updated,
        'user_updated', mesh_device_monitor.user_updated,
        'metadata', mesh_device_monitor.metadata,
        'notes', mesh_device_monitor.notes
      ) as reservoir_monitor,
      jsonb_build_object(
        'id', wp.id,
        'property', wp.property,
        'label', wp.label,
        'identifier', wp.identifier,
        'pump_type', wp.pump_type,
        'pump_model', wp.pump_model,
        'has_frequency_inverter', wp.has_frequency_inverter,
        'monitor_operation', wp.monitor_operation,
        'flow_rate_lh', wp.flow_rate_lh,
        'mode', wp.mode,
        'notes', wp.notes,
        'metadata', wp.metadata,
        'date_created', wp.date_created,
        'user_created', wp.user_created,
        'date_updated', wp.date_updated,
        'user_updated', wp.user_updated,
        'water_pump_controller', jsonb_build_object(
          'id', mesh_device_wp.id,
          'identifier', mesh_device_wp.identifier,
          'model', mesh_device_wp.model,
          'date_created', mesh_device_wp.date_created,
          'user_created', mesh_device_wp.user_created,
          'date_updated', mesh_device_wp.date_updated,
          'user_updated', mesh_device_wp.user_updated,
          'metadata', mesh_device_wp.metadata,
          'notes', mesh_device_wp.notes
        )
      ) as water_pump
    from device as lic_device
    inner join property_device as lic_property_device
      on lic_device.id = lic_property_device.device

    -- mesh mapping for reservoir monitor
    inner join mesh_device_mapping as mdm_monitor
      on lic_property_device.id = mdm_monitor.lic_property_device
    inner join property_device as mesh_property_device_monitor
      on mdm_monitor.mesh_property_device = mesh_property_device_monitor.id
    inner join device as mesh_device_monitor
      on mesh_property_device_monitor.device = mesh_device_monitor.id

    inner join reservoir
      on reservoir.reservoir_monitor = mesh_device_monitor.id

    -- water pump and its controller mapping
    inner join water_pump as wp
      on reservoir.water_pump = wp.id
    inner join mesh_device_mapping as mdm_wp
      on mdm_wp.lic_property_device = lic_property_device.id
    inner join property_device as mesh_property_device_wp
      on mdm_wp.mesh_property_device = mesh_property_device_wp.id
      and mesh_property_device_wp.device = wp.water_pump_controller
    inner join device as mesh_device_wp
      on mesh_property_device_wp.device = mesh_device_wp.id

    where
      lic_device.identifier = ${licIdentifier}
      and lic_device.model = 'LIC'

      -- lic property device active
      and ${referenceDate} >= COALESCE(lic_property_device.start_date, '-infinity'::timestamp)
      and ${referenceDate} <= COALESCE(lic_property_device.end_date, 'infinity'::timestamp)

      -- monitor mapping active
      and ${referenceDate} >= COALESCE(mdm_monitor.start_date, '-infinity'::timestamp)
      and ${referenceDate} <= COALESCE(mdm_monitor.end_date, 'infinity'::timestamp)
      and ${referenceDate} >= COALESCE(mesh_property_device_monitor.start_date, '-infinity'::timestamp)
      and ${referenceDate} <= COALESCE(mesh_property_device_monitor.end_date, 'infinity'::timestamp)

      -- water pump controller mapping active
      and ${referenceDate} >= COALESCE(mdm_wp.start_date, '-infinity'::timestamp)
      and ${referenceDate} <= COALESCE(mdm_wp.end_date, 'infinity'::timestamp)
      and ${referenceDate} >= COALESCE(mesh_property_device_wp.start_date, '-infinity'::timestamp)
      and ${referenceDate} <= COALESCE(mesh_property_device_wp.end_date, 'infinity'::timestamp)

      -- all mapped entities belong to the same property as the LIC
      and reservoir.property = lic_property_device.property
      and wp.property = lic_property_device.property
      and mesh_property_device_monitor.property = lic_property_device.property
      and mesh_property_device_wp.property = lic_property_device.property

    ORDER BY reservoir.date_created ASC
  `;
}

import { config as globalConfig } from "../config";
import { LoggerManager } from "../log";

export type DirectusClientConfig = {
  url: string;
  authToken: string;
};

const log = LoggerManager.getLogger("DirectusClient");
export class DirectusClient {
  constructor(
    private readonly config: DirectusClientConfig = globalConfig.directus
  ) {}

  clearCache() {
    return fetch(`${this.config.url}/utils/cache/clear`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${this.config.authToken}`,
      },
    })
      .then((response) => {
        log.debug("Directus cache updated:", response.statusText);
      })
      .catch((error) => {
        log.error("Error updating Directus cache:", error);
      });
  }
}

export const directusClient = new DirectusClient();

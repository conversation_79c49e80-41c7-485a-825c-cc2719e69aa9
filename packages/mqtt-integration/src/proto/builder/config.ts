import { codec } from "proto";
import {
  getCurrentPropertyForDevice,
  getPropertyDeviceMetadataForDevice,
} from "../../db/queries/property-device-queries";
import type { SQL } from "bun";

export async function buildConfig(
  db: SQL,
  licIdentifier: string,
  referenceDate: Date = new Date()
): Promise<codec.in_.config.ConfigPackage> {
  // First, find the property associated with this LIC device
  const property = await getCurrentPropertyForDevice(
    db,
    licIdentifier,
    "LIC",
    referenceDate
  );

  if (!property) {
    throw new Error(`No property found for LIC device ${licIdentifier}`);
  }

  // Build WiFi configuration if available
  let wifiConfig: codec.in_.config.WifiConfig | undefined;

  // For LIC devices, get WiFi credentials from property_device metadata instead of device metadata
  const metadata = await getPropertyDeviceMetadataForDevice(
    db,
    licIdentifier,
    "LIC",
    referenceDate
  );

  if (metadata) {
    if (metadata.wifiSSID && metadata.wifiPassword) {
      wifiConfig = codec.in_.config.WifiConfig.create({
        ssid: metadata.wifiSSID,
        password: metadata.wifiPassword,
      });
    }
  }

  // Build the ConfigPackage with property data
  const configPackage = codec.in_.config.ConfigPackage.create({
    backwash_cycle: property.backwash_period_minutes || 0,
    backwash_duration: property.backwash_duration_minutes || 0,
    backwash_delay: property.backwash_delay_seconds || 30, // Default delay of 30 seconds
    raingauge_enabled: property.rain_gauge_enabled || false,
    raingauge_factor: property.rain_gauge_resolution_mm
      ? Math.round(1.0 / property.rain_gauge_resolution_mm)
      : 5, // Default factor for 0.2mm resolution
    rainfall_limit: property.precipitation_volume_limit_mm || 2, // Default 2mm
    rainfall_pause_duration: property.precipitation_suspended_duration_hours
      ? property.precipitation_suspended_duration_hours * 60 // Convert hours to minutes
      : 1440, // Default 24 hours in minutes
    wifi: wifiConfig,
    // mesh configuration is not populated in the new system
  });

  return configPackage;
}

import type { SQL } from "bun";
import { codec } from "proto";
import { listFullProjectsByLICIdentifier } from "../../db/queries/project-queries";
import { listReservoirsWithMonitorAndWaterPumpByLICIdentifier } from "../../db/queries/reservoir";
import { listServiceWaterPumpsByLICIdentifier } from "../../db/queries/water-pump";

const DEV_TYPES = {
  Valve: 0,
  IrrigationPump: 1,
  Ferti: 2,
  Backwash: 3,
  ServicePump: 4,
  Level: 5,
} as const;

type ValveOutput = 1 | 2 | 3 | 4;
type VirtualDevice = {
  meshId: number;
  groupIdx: number;
  // Database record IDs for mapping protobuf elements back to database
  projectId?: string; // Maps to project.id
  deviceId?: string; // Maps to device.id (for physical devices like controllers)
  sectorId?: string; // Maps to sector.id (for valves)
  waterPumpId?: string; // Maps to water_pump.id (for pumps)
} & (
  | {
      // mainType: "VALVE";
      devType: typeof DEV_TYPES.Valve;
      output: ValveOutput;
      /**
       * between 0 and 100
       */
      power: number;
    }
  | {
      // mainType: "LEVEL";
      devType: typeof DEV_TYPES.Level;
    }
  | ({
      // mainType: "PUMP";
      model: "PL10" | "PL50";
      mode: "PULSE" | "CONTINUOUS";
    } & (
      | {
          devType: typeof DEV_TYPES.IrrigationPump;
          monitorOperation: boolean;
        }
      | {
          devType: typeof DEV_TYPES.Ferti;
        }
      | {
          devType: typeof DEV_TYPES.Backwash;
        }
      | {
          devType: typeof DEV_TYPES.ServicePump;
          monitorOperation: boolean;
        }
    ))
);

export async function buildDevices(
  db: SQL,
  licIdentifier: string,
  referenceDate: Date = new Date()
): Promise<{
  package: codec.in_.devices.DevicesPackage;
  metadata: {
    groupIdxToProjectId: Map<number, string>;
    deviceIdxToDeviceId: Map<number, string>;
  };
}> {
  const virtualDevices = await generateVirtualDevices(
    db,
    licIdentifier,
    referenceDate
  );
  const { data, metadata } = virtualDevicesToData(virtualDevices);
  const result = codec.in_.devices.DevicesPackage.create({
    data,
  });
  return {
    package: result,
    metadata,
  };
}

export async function generateVirtualDevices(
  db: SQL,
  licIdentifier: string,
  referenceDate: Date
) {
  const projects = await listFullProjectsByLICIdentifier(
    db,
    licIdentifier,
    referenceDate
  );
  const virtualDevices: VirtualDevice[] = [];

  for (let i = 0; i < projects.length; i++) {
    const project = projects[i]!;
    // groupIdx starts from 1. groupIdx zero is reserved for service pumps and reservoirs
    const groupIdx = i + 1;

    if (project.irrigation_water_pump) {
      // Irrigation water pump
      const irrigationPump = project.irrigation_water_pump;
      const pumpLink = irrigationPump.water_pump_controller;

      if (pumpLink.model !== "WPC-PL10" && pumpLink.model !== "WPC-PL50") {
        //throw new Error(`Invalid pump link model: ${pumpLink.model}`);
        continue;
      }

      virtualDevices.push({
        meshId: Number.parseInt(pumpLink.identifier, 16),
        groupIdx,
        projectId: project.id,
        deviceId: pumpLink.id,
        waterPumpId: irrigationPump.id,
        // mainType: "PUMP",
        devType: DEV_TYPES.IrrigationPump,
        model: pumpLink.model == "WPC-PL10" ? "PL10" : "PL50",
        mode: irrigationPump.mode,
        monitorOperation: irrigationPump.monitor_operation,
      });
    } else {
      // throw new Error("Invalid project - No irrigation pump");
      continue;
    }

    if (project.fertigation_water_pump) {
      // Irrigation water pump
      const fertigationPump = project.fertigation_water_pump;
      const fertigationPumpLink = fertigationPump.water_pump_controller;

      if (
        fertigationPumpLink.model !== "WPC-PL10" &&
        fertigationPumpLink.model !== "WPC-PL50"
      ) {
        //throw new Error(`Invalid pump link model: ${fertigationPumpLink.model}`);
        continue;
      }

      virtualDevices.push({
        meshId: Number.parseInt(fertigationPumpLink.identifier, 16),
        groupIdx,
        projectId: project.id,
        deviceId: fertigationPumpLink.id,
        waterPumpId: fertigationPump.id,
        // mainType: "PUMP",
        devType: DEV_TYPES.Ferti,
        model: fertigationPumpLink.model == "WPC-PL10" ? "PL10" : "PL50",
        mode: fertigationPump.mode,
      });
    }

    // Handle backwash pump based on project configuration
    const backwashPump =
      project.backwash_pump_type == null
        ? null
        : project.backwash_pump_type === "IRRIGATION"
        ? project.irrigation_water_pump
        : project.fertigation_water_pump;
    if (backwashPump) {
      // Use the irrigation pump for backwashing
      const pumpLink = backwashPump.water_pump_controller;
      virtualDevices.push({
        meshId: Number.parseInt(pumpLink.identifier, 16),
        groupIdx,
        projectId: project.id,
        deviceId: pumpLink.id,
        waterPumpId: backwashPump.id,
        // mainType: "PUMP",
        devType: DEV_TYPES.Backwash,
        model: pumpLink.model == "WPC-PL10" ? "PL10" : "PL50",
        mode: project.irrigation_water_pump.mode,
      });
    }

    // Valve controllers (VC)
    if (project.sectors) {
      for (const sector of project.sectors) {
        const vcDevice = sector.valve_controller_device;
        if (vcDevice && sector.valve_controller_output != null) {
          virtualDevices.push({
            meshId: Number.parseInt(vcDevice.identifier, 16),
            groupIdx,
            projectId: project.id,
            deviceId: vcDevice.id,
            sectorId: sector.id,
            // mainType: "VALVE",
            devType: DEV_TYPES.Valve,
            output: sector.valve_controller_output as ValveOutput,
            power: sector.power ?? 0,
          });
        }
      }
    }
  }

  // Service water pumps
  const servicePumps = await listServiceWaterPumpsByLICIdentifier(
    db,
    licIdentifier,
    referenceDate
  );
  servicePumps.forEach((servicePump) => {
    if (servicePump.water_pump_controller) {
      if (
        servicePump.water_pump_controller.model !== "WPC-PL10" &&
        servicePump.water_pump_controller.model !== "WPC-PL50"
      ) {
        //throw new Error(`Invalid service pump controller model: ${servicePump.controller.model}`);
        return;
      }
      virtualDevices.push({
        meshId: Number.parseInt(
          servicePump.water_pump_controller.identifier,
          16
        ),
        groupIdx: 0,
        deviceId: servicePump.water_pump_controller.id,
        waterPumpId: servicePump.id,
        // mainType: "PUMP",
        devType: DEV_TYPES.ServicePump,
        model:
          servicePump.water_pump_controller.model == "WPC-PL10"
            ? "PL10"
            : "PL50",
        mode: servicePump.mode,
        monitorOperation: servicePump.monitor_operation,
      });
    }
  });
  // Reservoirs with monitor and water pump
  const reservoirs = await listReservoirsWithMonitorAndWaterPumpByLICIdentifier(
    db,
    licIdentifier,
    referenceDate
  );
  reservoirs.forEach((reservoir) => {
    if (reservoir.reservoir_monitor) {
      if (reservoir.reservoir_monitor.model !== "RM") {
        //throw new Error(`Invalid reservoir monitor model: ${reservoir.reservoir_monitor.model}`);
        return;
      }
      virtualDevices.push({
        meshId: Number.parseInt(reservoir.reservoir_monitor.identifier, 16),
        groupIdx: 0,
        deviceId: reservoir.reservoir_monitor.id,
        // mainType: "LEVEL",
        devType: DEV_TYPES.Level,
      });
    }
  });
  return virtualDevices;
}

function virtualDevicesToData(virtualDevices: VirtualDevice[]): {
  data: codec.in_.devices.IDevicesData[];
  metadata: {
    groupIdxToProjectId: Map<number, string>;
    deviceIdxToDeviceId: Map<number, string>;
  };
} {
  const data: codec.in_.devices.IDevicesData[] = [];

  // Initialize metadata maps
  const groupIdxToProjectId = new Map<number, string>();
  const deviceIdxToDeviceId = new Map<number, string>();

  const virtualDevicesByGroup = Object.groupBy(
    virtualDevices.toSorted((a, b) => {
      return a.groupIdx - b.groupIdx || a.meshId - b.meshId;
    }),
    (device) => device.groupIdx
  );
  let idx = 0;
  Object.entries(virtualDevicesByGroup).forEach(([groupIdx, devices]) => {
    if (devices) {
      const virtualDevicesByMesh = Object.groupBy(
        devices,
        (device) => device.meshId
      );
      let sector = 1;
      Object.entries(virtualDevicesByMesh).forEach(
        ([meshId, devicesByMesh]) => {
          if (devicesByMesh?.length) {
            const firstDevice = devicesByMesh[0]!;

            // Protobuf DevicesData field -> Android sqlite table field -> Irriga Mais value mapping
            // idx -> Devices.ord_idx -> No direct mapping, global virtual device sequence from 0
            // meshId -> Mesh_Devices.mesh_id -> table device, column identifier, int base 16
            // deviceId -> Devices.identity -> No direct mapping, sequence from 0 of virtual devices of mesh devices, usually represents a mesh device output
            // deviceType -> Devices.type -> No direct mapping, use DevType enum: Valve=0, IrrigationPump=1, Ferti=2, Backwash=3, ServicePump=4, Level=5
            // out1 -> Devices.out1 -> No direct mapping, Depends on the DevType: IrrigationPump=1, Ferti=2, Backwash=3, ServicePump=1, Level=0, Valve=(2*deviceId + 1)
            // out2 -> Devices.out2 -> No direct mapping, Depends on the DevType and Pumplink type: IrrigationPump=<PL10=0,PL50=isPulse?2:0>, Ferti=0, Backwash=0, ServicePump=<PL10=0,PL50=isPulse?2:0>, Level=0, Valve=(2*deviceId + 2)
            // input -> Devices.input -> if IrrigationPump or ServicePump: (table water_pump, column monitor_operation is true ? 1 : 0) else: 0
            // mode -> Devices.mode -> if Valve: 1, else if Level: 0, else if Ferti or Backwash: 0, else: (if (table water_pump, column mode is PULSE) 1 else 0) | (if (table water_pump, column monitor_operation is true) 2 else 0)
            // sector -> Devices.sector -> If not Valve: null, else: project's sector sequence from 0
            // groupIdx -> Mesh_Devices.group_idx -> project sequence from 0
            // equipment -> Devices.eqpt_ver -> null
            // power -> Devices.power -> table sector, column power (int between 0 and 100)
            //

            if (firstDevice.devType === DEV_TYPES.Valve) {
              const valveData: codec.in_.devices.IDevicesData[] = (
                devicesByMesh as Array<typeof firstDevice>
              )
                .toSorted((a, b) => a.output - b.output)
                .map((device, index) => {
                  const deviceIdx = idx++;

                  // Populate metadata maps
                  if (device.projectId) {
                    groupIdxToProjectId.set(device.groupIdx, device.projectId);
                  }
                  if (device.deviceId) {
                    deviceIdxToDeviceId.set(deviceIdx, device.deviceId);
                  }

                  return {
                    idx: deviceIdx,
                    meshId: device.meshId,
                    groupIdx: device.groupIdx,
                    deviceId: index,
                    deviceType: device.devType,
                    out1: 2 * index + 1,
                    out2: 2 * index + 2,
                    input: 0,
                    mode: 1, // Valve
                    sector: sector++,
                    equipment: null,
                    power: device.power,
                  };
                });
              data.push(...valveData);
            } else if (firstDevice.devType === DEV_TYPES.Level) {
              const levelData: codec.in_.devices.IDevicesData[] = (
                devicesByMesh as Array<typeof firstDevice>
              ).map((device, index) => {
                const deviceIdx = idx++;

                // Populate metadata maps
                if (device.projectId) {
                  groupIdxToProjectId.set(device.groupIdx, device.projectId);
                }
                if (device.deviceId) {
                  deviceIdxToDeviceId.set(deviceIdx, device.deviceId);
                }

                return {
                  idx: deviceIdx,
                  meshId: device.meshId,
                  groupIdx: device.groupIdx,
                  deviceId: 0, // Only one virtual level device per physical device
                  deviceType: device.devType,
                  out1: 0,
                  out2: 0,
                  input: 0,
                  mode: 0, // Level
                  sector: null,
                  equipment: null,
                  power: null,
                };
              });
              data.push(...levelData);
            } else {
              const pumpData: codec.in_.devices.IDevicesData[] = (
                devicesByMesh as Array<typeof firstDevice>
              )
                .toSorted((a, b) => a.devType - b.devType)
                .map((device, index) => {
                  const deviceIdx = idx++;

                  // Populate metadata maps
                  if (device.projectId) {
                    groupIdxToProjectId.set(device.groupIdx, device.projectId);
                  }
                  if (device.deviceId) {
                    deviceIdxToDeviceId.set(deviceIdx, device.deviceId);
                  }

                  return {
                    idx: deviceIdx,
                    meshId: device.meshId,
                    groupIdx: device.groupIdx,
                    deviceId: index,
                    deviceType: device.devType,
                    out1:
                      device.devType === DEV_TYPES.IrrigationPump
                        ? 1
                        : device.devType === DEV_TYPES.Ferti
                        ? 2
                        : 3,
                    // out2 -> Devices.out2 -> No direct mapping, Depends on the DevType and Pumplink type: IrrigationPump=<PL10=0,PL50=isPulse?2:0>, Ferti=0, Backwash=0, ServicePump=<PL10=0,PL50=isPulse?2:0>, Level=0, Valve=(2*deviceId + 2)
                    out2:
                      device.devType === DEV_TYPES.IrrigationPump
                        ? device.model === "PL50" && device.mode === "PULSE"
                          ? 2
                          : 0
                        : 0,
                    // input -> Devices.input -> if IrrigationPump or ServicePump: (table water_pump, column monitor_operation is true ? 1 : 0) else: 0
                    input:
                      device.devType === DEV_TYPES.IrrigationPump ||
                      device.devType === DEV_TYPES.ServicePump
                        ? device.monitorOperation
                          ? 1
                          : 0
                        : 0,
                    // mode -> Devices.mode -> if Valve: 1, else if Level: 0, else if Ferti or Backwash: 0, else: (if (table water_pump, column mode is PULSE) 1 else 0) | (if (table.water_pump, column monitor_operation is true) 2 else 0)
                    mode:
                      device.devType === DEV_TYPES.IrrigationPump ||
                      device.devType === DEV_TYPES.ServicePump
                        ? (device.mode === "PULSE" ? 1 : 0) |
                          (device.monitorOperation ? 2 : 0)
                        : 0,
                    sector: null,
                    equipment: null,
                    power: null,
                  };
                });
              data.push(...pumpData);
            }
          }
        }
      );
    }
  });

  return {
    data,
    metadata: {
      groupIdxToProjectId,
      deviceIdxToDeviceId,
    },
  };
}

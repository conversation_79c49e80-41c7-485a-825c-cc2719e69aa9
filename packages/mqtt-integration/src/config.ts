export class Config {
  // We do not handle log config because log module will be extracted into a new package and will no have access to this Config class
  //   log = {
  //     level: process.env.LOG_LEVEL || "info",
  //     childrenLevel: process.env.CHILDREN_LOG_LEVEL,
  //   };
  mqtt = {
    brokerUrl: process.env.MQTT_BROKER_URL || "mqtt://localhost:1883",
    username: process.env.MQTT_USERNAME || "user",
    password: process.env.MQTT_PASSWORD || "password",
  };
  postgres = {
    url: process.env.POSTGRES_URL || "postgres://localhost:5432/irriga_mais",
  };
  directus = {
    url: process.env.DIRECTUS_URL || "http://localhost:8055",
    authToken: process.env.DIRECTUS_AUTH_TOKEN || "",
  };
  deviceMessageQueue = {
    pollingIntervalMs: Number(
      process.env.DEVICE_MESSAGE_QUEUE_POLLING_INTERVAL_MS || 1000
    ),
    batchSize: Number(process.env.DEVICE_MESSAGE_QUEUE_BATCH_SIZE || 50),
    retryBatchSize: Number(
      process.env.DEVICE_MESSAGE_QUEUE_RETRY_BATCH_SIZE || 20
    ),
    enableRetryProcessing:
      process.env.DEVICE_MESSAGE_QUEUE_ENABLE_RETRY_PROCESSING?.toLowerCase() ===
      "true",
  };
  test_postgres = {
    url: process.env.TEST_POSTGRES_URL || "postgres://localhost:5432/test_db",
  };
}

export const config = new Config();

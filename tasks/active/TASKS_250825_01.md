# Task list info:

- name: 250825_01
- base_branch: develop

---

# Tasks

## Task 1. Plan the LIC simulator

**Description**
We need a LIC simulator to test the MQTT integration.
It should be able to connect to the MQTT broker, subscribe to the topics, and handle the messages.
It should also be able to publish messages to the topics.
The main purpose is to avoid the necessity of having a real LIC device to test the integration. Thus, it does not need to implement the LoRa mesh network or communicate with other devices, but it should be able to handle the protobuf messages and simulate a real LIC behavior.
The task is to create a detailed plan of what the simulator should be able to do and how it should be implemented. (Bun and TypeScript will be used in the implementation).

Some relevant files to consider when creating the plan:

- /engineering/irriganet.md - information about the Android simplified configuration tool
- /engineering/codec_wifi - firmware code for the LIC device (ESP32 firmware)
- /packages/protobuf - Bun package with protobuf messages definition and types generated from it
- /packages/mqtt-integration - Bun package with MQTT integration code
- /docs/000-DESCRIPTION.md - project documentation
- docs/PRODUCT_OVERVIEW.md - product overview
- docs/STRUCTURE-OVERVIEW.md - project structure overview

A note on protobuf messages:

- codecs/incoming_packet.proto is the root message that is sent to the LIC.
- codecs/outgoing_packet.proto is the root message that is sent from the LIC.

What is expected from the simulator:

- It must behave exactly like a real LIC device.
- It should be able to connect to the MQTT broker, subscribe to the topics, and handle the messages.
- It should also be able to publish messages to the topics.
- It should be able to handle the protobuf messages and simulate a real LIC behavior.
- It should store the state of the LIC in file.

Th

**Target directories**

- docs (documentation)
- packages (code)

**Status:** Pending

# Main tables definition for the irrigation management system

This file contains the SQL DDL statements to create the necessary tables and relationships. The structure follows the Entity-Relationship diagram defined in `002-ENTITY_DIAGRAMS.md`.

## Database Schema Overview

The database schema includes the following main entities:

- **Account**: User account identified by owner information
- **Account_User**: Junction table linking users to accounts with roles and temporal validity
- **Property**: Physical properties owned by accounts
- **Device**: Hardware devices (LIC, WPC-PL10, WPC-PL50, VC, RM) with unique identifiers
- **Property_Device**: Junction table linking devices to properties with temporal validity
- **Mesh_Device_Mapping**: Association table linking a mesh Property_Device to a LIC Property_Device with temporal validity
- **Current_Lic_Packet**: Current state packets from LIC devices (latest per device/payload_type)
- **Lic_Packet**: TimescaleDB hypertable storing complete historical LIC packet data
- **Device_Message_Request**: Queue for outgoing protobuf messages to LIC devices via MQTT
- **Water_Pump**: Water pumps associated with properties for irrigation, fertigation, or service
- **Reservoir**: Water reservoirs within properties for water storage
- **Project**: Irrigation projects within properties
- **Sector**: Individual irrigation sectors within projects
- **Irrigation_Plan**: Scheduled irrigation plans for projects
- **Irrigation_Plan_Step**: Individual steps within irrigation plans

---

### Table: directus_users

```sql
CREATE TABLE IF NOT EXISTS public.directus_users
(
    id uuid NOT NULL,
    first_name character varying(50) ,
    last_name character varying(50) ,
    email character varying(128) ,
    password character varying(255) ,
    location character varying(255) ,
    title character varying(50) ,
    description text ,
    tags json,
    avatar uuid,
    language character varying(255)  DEFAULT NULL::character varying,
    tfa_secret character varying(255) ,
    status character varying(16)  NOT NULL DEFAULT 'active'::character varying,
    role uuid,
    token character varying(255) ,
    last_access timestamp with time zone,
    last_page character varying(255) ,
    provider character varying(128)  NOT NULL DEFAULT 'default'::character varying,
    external_identifier character varying(255) ,
    auth_data json,
    email_notifications boolean DEFAULT true,
    appearance character varying(255) ,
    theme_dark character varying(255) ,
    theme_light character varying(255) ,
    theme_light_overrides json,
    theme_dark_overrides json,
    CONSTRAINT directus_users_pkey PRIMARY KEY (id),
    CONSTRAINT directus_users_email_unique UNIQUE (email),
    CONSTRAINT directus_users_external_identifier_unique UNIQUE (external_identifier),
    CONSTRAINT directus_users_token_unique UNIQUE (token),
    CONSTRAINT directus_users_role_foreign FOREIGN KEY (role)
        REFERENCES public.directus_roles (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE SET NULL
)
```

---

### Table: device

```sql
CREATE TABLE IF NOT EXISTS public.device
(
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    identifier character varying(255)  NOT NULL,
    model character varying(16)  NOT NULL,
    date_created timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_created uuid,
    date_updated timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_updated uuid,
    metadata jsonb,
    notes text,
    CONSTRAINT device_pkey PRIMARY KEY (id),
    CONSTRAINT device_identifier_unique UNIQUE (identifier),
    CONSTRAINT device_model_check CHECK (model IN ('LIC', 'WPC-PL10', 'WPC-PL50', 'VC', 'RM')),
    CONSTRAINT device_user_created_foreign FOREIGN KEY (user_created)
        REFERENCES public.directus_users (id) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE RESTRICT,
    CONSTRAINT device_user_updated_foreign FOREIGN KEY (user_updated)
        REFERENCES public.directus_users (id) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE RESTRICT
);

CREATE OR REPLACE TRIGGER set_device_date_updated
    BEFORE UPDATE
    ON public.device
    FOR EACH ROW
    EXECUTE FUNCTION public.update_timestamp_column();
```

---

### Table: account

```sql
CREATE TABLE IF NOT EXISTS public.account
(
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    owner uuid NOT NULL,
    date_created timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_created uuid,
    date_updated timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_updated uuid,
    metadata jsonb,
    notes text,
    CONSTRAINT account_pkey PRIMARY KEY (id),
    CONSTRAINT account_owner_unique UNIQUE (owner),
    CONSTRAINT account_owner_foreign FOREIGN KEY (owner)
        REFERENCES public.directus_users (id) MATCH SIMPLE
        ON DELETE RESTRICT,
    CONSTRAINT account_user_created_foreign FOREIGN KEY (user_created)
        REFERENCES public.directus_users (id) MATCH SIMPLE
        ON DELETE RESTRICT,
    CONSTRAINT account_user_updated_foreign FOREIGN KEY (user_updated)
        REFERENCES public.directus_users (id) MATCH SIMPLE
        ON DELETE RESTRICT
);

CREATE OR REPLACE TRIGGER set_account_date_updated
    BEFORE UPDATE
    ON public.account
    FOR EACH ROW
    EXECUTE FUNCTION public.update_timestamp_column();
```

---

### Table: property

```sql
CREATE TABLE IF NOT EXISTS public.property
(
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    account uuid NOT NULL,
    name character varying(255)  NOT NULL,
    backwash_duration_minutes integer,
    backwash_period_minutes integer,
    backwash_delay_seconds integer,
    rain_gauge_enabled boolean DEFAULT false NOT NULL,
    rain_gauge_resolution_mm real DEFAULT '0.2'::real,
    precipitation_volume_limit_mm real DEFAULT '2'::real,
    precipitation_suspended_duration_hours real DEFAULT '24'::real,
    timezone character varying(255)  NOT NULL DEFAULT 'America/Sao_Paulo'::character varying,
    point geometry(Point,4326),
    address_postal_code character varying(255) ,
    address_street_name character varying(255) ,
    address_street_number character varying(255) ,
    address_complement character varying(255) ,
    address_neighborhood character varying(255) ,
    address_city character varying(255) ,
    address_state character varying(255) ,
    address_country character varying(255)  DEFAULT 'Brasil'::character varying,
    notes text ,
    date_created timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_created uuid,
    date_updated timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_updated uuid,
    metadata jsonb,
    CONSTRAINT property_pkey PRIMARY KEY (id),
    CONSTRAINT property_account_name_unq UNIQUE (account, name),
    CONSTRAINT property_account_foreign FOREIGN KEY (account)
        REFERENCES public.account (id) MATCH SIMPLE
        ON DELETE RESTRICT,
    CONSTRAINT property_user_created_foreign FOREIGN KEY (user_created)
        REFERENCES public.directus_users (id) MATCH SIMPLE
        ON DELETE RESTRICT,
    CONSTRAINT property_user_updated_foreign FOREIGN KEY (user_updated)
        REFERENCES public.directus_users (id) MATCH SIMPLE
        ON DELETE RESTRICT
);

CREATE OR REPLACE TRIGGER set_property_date_updated
    BEFORE UPDATE
    ON public.property
    FOR EACH ROW
    EXECUTE FUNCTION public.update_timestamp_column();
```

---

### Table: property_device

```sql
CREATE TABLE IF NOT EXISTS public.property_device
(
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    device uuid NOT NULL,
    property uuid NOT NULL,
    start_date timestamp with time zone NOT NULL,
    end_date timestamp with time zone,
    current_mesh_device_mapping uuid, -- New: holds active mesh_device_mapping for mesh PDs
    date_created timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_created uuid,
    date_updated timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_updated uuid,
    metadata jsonb,
    notes text,
    CONSTRAINT property_device_pkey PRIMARY KEY (id),
    CONSTRAINT property_device_device_foreign FOREIGN KEY (device)
        REFERENCES public.device (id) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE RESTRICT,
    CONSTRAINT property_device_property_foreign FOREIGN KEY (property)
        REFERENCES public.property (id) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE RESTRICT,
    CONSTRAINT property_device_current_mesh_mapping_foreign FOREIGN KEY (current_mesh_device_mapping)
        REFERENCES public.mesh_device_mapping (id) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE SET NULL,
    CONSTRAINT property_device_user_created_foreign FOREIGN KEY (user_created)
        REFERENCES public.directus_users (id) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE RESTRICT,
    CONSTRAINT property_device_user_updated_foreign FOREIGN KEY (user_updated)
        REFERENCES public.directus_users (id) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE RESTRICT,
    CONSTRAINT property_device_no_overlap EXCLUDE USING gist (
        device WITH =,
        tstzrange(start_date, end_date) WITH &&)
);

CREATE INDEX IF NOT EXISTS property_device_property_index
    ON public.property_device USING btree
    (property ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS property_device_device_index
    ON public.property_device USING btree
    (device ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS property_device_current_mesh_idx
    ON public.property_device USING btree
    (current_mesh_device_mapping ASC NULLS LAST);

CREATE OR REPLACE TRIGGER set_property_device_date_updated
    BEFORE UPDATE
    ON public.property_device
    FOR EACH ROW
    EXECUTE FUNCTION public.update_timestamp_column();
```

---

### Table: mesh_device_mapping

```sql
CREATE TABLE IF NOT EXISTS public.mesh_device_mapping
(
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    mesh_property_device uuid NOT NULL,
    lic_property_device uuid NOT NULL,
    start_date timestamp with time zone NOT NULL,
    end_date timestamp with time zone,
    date_created timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    user_created uuid,
    date_updated timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    user_updated uuid,
    CONSTRAINT mesh_device_mapping_pkey PRIMARY KEY (id),
    CONSTRAINT mesh_device_mapping_mesh_pd_foreign FOREIGN KEY (mesh_property_device)
        REFERENCES public.property_device (id) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE RESTRICT,
    CONSTRAINT mesh_device_mapping_lic_pd_foreign FOREIGN KEY (lic_property_device)
        REFERENCES public.property_device (id) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE RESTRICT
);

-- Maintain date_updated
CREATE OR REPLACE TRIGGER set_mesh_device_mapping_date_updated
    BEFORE UPDATE
    ON public.mesh_device_mapping
    FOR EACH ROW
    EXECUTE FUNCTION public.update_timestamp_column();

-- Helpful indexes
CREATE INDEX IF NOT EXISTS mesh_device_mapping_mesh_start_idx
    ON public.mesh_device_mapping USING btree (mesh_property_device, start_date);

CREATE INDEX IF NOT EXISTS mesh_device_mapping_mesh_end_idx
    ON public.mesh_device_mapping USING btree (mesh_property_device, end_date);
```

---

### Function: im_update_current_mesh_device_mapping

```sql
CREATE OR REPLACE FUNCTION im_update_current_mesh_device_mapping(mesh_ids uuid[], reference_date timestamptz DEFAULT now())
RETURNS void
LANGUAGE plpgsql
AS $$
DECLARE
  mesh_id uuid;
  active_mapping_id uuid;
BEGIN
  IF mesh_ids IS NULL OR array_length(mesh_ids, 1) IS NULL THEN
    RETURN;
  END IF;

  FOREACH mesh_id IN ARRAY mesh_ids LOOP
    SELECT mdm.id
      INTO active_mapping_id
    FROM mesh_device_mapping mdm
    WHERE mdm.mesh_property_device = mesh_id
      AND mdm.start_date <= reference_date
      AND (mdm.end_date IS NULL OR reference_date <= mdm.end_date)
    ORDER BY mdm.start_date DESC
    LIMIT 1;

    UPDATE property_device pd
      SET current_mesh_device_mapping = active_mapping_id
    WHERE pd.id = mesh_id;
  END LOOP;
END;
$$;
```

---

### Function: trg_mesh_device_mapping_enforce

```sql
CREATE OR REPLACE FUNCTION trg_mesh_device_mapping_enforce() RETURNS trigger
LANGUAGE plpgsql
AS $$
DECLARE
  v_mesh_property uuid;
  v_lic_property uuid;
  v_mesh_device uuid;
  v_lic_device uuid;
  v_mesh_model text;
  v_lic_model text;
  ref_end timestamptz;
  r RECORD;
  orig_end timestamptz;
BEGIN
  -- Normalize NEW.end_date for comparisons
  ref_end := COALESCE(NEW.end_date, 'infinity'::timestamptz);

  -- Basic sanity: mapping must not be self-mapping
  IF NEW.mesh_property_device = NEW.lic_property_device THEN
    RAISE EXCEPTION 'mesh_property_device must be different from lic_property_device';
  END IF;

  -- Same property check and capture device ids
  SELECT pd.property, pd.device INTO v_mesh_property, v_mesh_device
  FROM property_device pd WHERE pd.id = NEW.mesh_property_device;
  IF v_mesh_property IS NULL THEN
    RAISE EXCEPTION 'mesh_property_device % does not exist', NEW.mesh_property_device;
  END IF;

  SELECT pd.property, pd.device INTO v_lic_property, v_lic_device
  FROM property_device pd WHERE pd.id = NEW.lic_property_device;
  IF v_lic_property IS NULL THEN
    RAISE EXCEPTION 'lic_property_device % does not exist', NEW.lic_property_device;
  END IF;

  IF v_mesh_property <> v_lic_property THEN
    RAISE EXCEPTION 'mesh and LIC property_device must belong to the same property';
  END IF;

  -- Device models: mesh must be WPC-PL10, WPC-PL50, VC, or RM; LIC must be LIC
  SELECT d.model INTO v_mesh_model FROM device d WHERE d.id = v_mesh_device;
  SELECT d.model INTO v_lic_model FROM device d WHERE d.id = v_lic_device;

  IF v_lic_model <> 'LIC' THEN
    RAISE EXCEPTION 'lic_property_device must reference a device with model=LIC';
  END IF;

  IF v_mesh_model NOT IN ('WPC-PL10', 'WPC-PL50', 'VC', 'RM') THEN
    RAISE EXCEPTION 'mesh_property_device must reference a mesh-capable device (WPC-PL10, WPC-PL50, VC, RM)';
  END IF;

  -- Devices active at association time: ensure both PD rows cover NOW()
  -- Mesh PD must be active now
  PERFORM 1 FROM property_device pd
   WHERE pd.id = NEW.mesh_property_device
     AND pd.start_date <= now()
     AND (pd.end_date IS NULL OR now() <= pd.end_date);
  IF NOT FOUND THEN
    RAISE EXCEPTION 'mesh_property_device must be active at association time (NOW())';
  END IF;

  -- LIC PD must be active now
  PERFORM 1 FROM property_device pd
   WHERE pd.id = NEW.lic_property_device
     AND pd.start_date <= now()
     AND (pd.end_date IS NULL OR now() <= pd.end_date);
  IF NOT FOUND THEN
    RAISE EXCEPTION 'lic_property_device must be active at association time (NOW())';
  END IF;

  -- Accommodation logic for overlapping mappings for same mesh_property_device
  -- Loop over overlapping existing rows
  FOR r IN
    SELECT *
    FROM mesh_device_mapping m
    WHERE m.mesh_property_device = NEW.mesh_property_device
      AND m.id <> COALESCE(NEW.id, '00000000-0000-0000-0000-000000000000'::uuid)
      AND NOT (
        COALESCE(m.end_date, 'infinity'::timestamptz) <= NEW.start_date
        OR ref_end <= COALESCE(m.start_date, '-infinity'::timestamptz)
      )
    ORDER BY m.start_date
  LOOP
    orig_end := r.end_date;

    -- Case 3: existing entirely contained within new -> error
    IF r.start_date >= NEW.start_date AND (COALESCE(r.end_date, 'infinity'::timestamptz) <= ref_end) THEN
      RAISE EXCEPTION 'There is already an association active in the requested period';
    END IF;

    -- Case 4: existing contains new -> split existing into two
    IF r.start_date < NEW.start_date AND (COALESCE(r.end_date, 'infinity'::timestamptz) > ref_end) THEN
      -- Left piece: set end to NEW.start_date
      UPDATE mesh_device_mapping
        SET end_date = NEW.start_date
      WHERE id = r.id;

      -- Right piece: create a new row starting at NEW.end_date + 1s up to original end
      IF NEW.end_date IS NULL THEN
        -- new has no end; right piece would be empty; skip creation
      ELSE
        INSERT INTO mesh_device_mapping(
          id, mesh_property_device, lic_property_device, start_date, end_date, date_created, user_created, date_updated, user_updated
        ) VALUES (
          gen_random_uuid(), r.mesh_property_device, r.lic_property_device, NEW.end_date + interval '1 second', orig_end, now(), NULL, now(), NULL
        );
      END IF;

    -- Case 1: existing starts before new starts -> truncate existing.end_date to NEW.start_date
    ELSIF r.start_date < NEW.start_date THEN
      UPDATE mesh_device_mapping
        SET end_date = NEW.start_date
      WHERE id = r.id;

    -- Case 2: existing ends after new ends -> move existing.start_date to NEW.end_date + 1s
    ELSIF COALESCE(r.end_date, 'infinity'::timestamptz) > ref_end THEN
      IF NEW.end_date IS NULL THEN
        -- new has no end; thus existing cannot remain; move start after infinity is meaningless.
        RAISE EXCEPTION 'There is already an association active in the requested period';
      ELSE
        UPDATE mesh_device_mapping
          SET start_date = NEW.end_date + interval '1 second'
        WHERE id = r.id;
      END IF;
    END IF;
  END LOOP;

  -- After accommodation, ensure a mesh has only one LIC at a time for the new record window
  -- Overlaps still present here indicate logic error
  PERFORM 1
  FROM mesh_device_mapping m
  WHERE m.mesh_property_device = NEW.mesh_property_device
    AND m.id <> COALESCE(NEW.id, '00000000-0000-0000-0000-000000000000'::uuid)
    AND NOT (
      COALESCE(m.end_date, 'infinity'::timestamptz) <= NEW.start_date
      OR ref_end <= COALESCE(m.start_date, '-infinity'::timestamptz)
    );
  IF FOUND THEN
    RAISE EXCEPTION 'mesh_device_mapping records must not overlap for the same mesh_property_device';
  END IF;

  -- Do NOT call updater here; final image not persisted yet in BEFORE trigger.
  RETURN NEW;
END;
$$;
```

---

### Function: trg_mesh_device_mapping_after_recalc

```sql
CREATE OR REPLACE FUNCTION trg_mesh_device_mapping_after_recalc() RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
  PERFORM im_update_current_mesh_device_mapping(ARRAY[NEW.mesh_property_device], now());
  RETURN NEW;
END;
$$;
```

---

### Trigger Function: trg_mesh_device_mapping_enforce (business rules)

Summary of logic (see migration for full source):

- Ensure mesh_property_device and lic_property_device belong to the same property.
- Ensure device models: LIC model = 'LIC'; mesh models in ('WPC-PL10', 'WPC-PL50', 'VC', 'RM').
- Ensure both PD rows are active at NOW().
- Accommodation logic on insert/update to prevent overlap for a given mesh PD:
  - Existing contained within new: raise exception.
  - Existing contains new: split into two segments (left truncated to new.start_date and right starting at new.end_date + 1s).
  - Existing starts before new: truncate existing.end_date to new.start_date.
  - Existing ends after new: shift existing.start_date to new.end_date + 1s (error if new is infinite).
- After adjustments, validates no remaining overlap.
- Calls im_update_current_mesh_device_mapping for the affected mesh PD.

Triggers:

```sql
CREATE TRIGGER trg_mesh_device_mapping_insert
BEFORE INSERT ON public.mesh_device_mapping
FOR EACH ROW
EXECUTE FUNCTION public.trg_mesh_device_mapping_enforce();

CREATE TRIGGER trg_mesh_device_mapping_update
BEFORE UPDATE OF mesh_property_device, lic_property_device, start_date, end_date
ON public.mesh_device_mapping
FOR EACH ROW
EXECUTE FUNCTION public.trg_mesh_device_mapping_enforce();

CREATE TRIGGER trg_mesh_device_mapping_after
AFTER INSERT OR UPDATE ON public.mesh_device_mapping
FOR EACH ROW
EXECUTE FUNCTION public.trg_mesh_device_mapping_after_recalc();
```

---

### Table: current_lic_packet

```sql
CREATE TABLE IF NOT EXISTS public.current_lic_packet
(
    id SERIAL PRIMARY KEY,
    device uuid NOT NULL,
    date_created timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    packet_date timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    payload_type character varying(50) NOT NULL,
    payload_data jsonb NOT NULL,
    CONSTRAINT current_lic_packet_device_foreign FOREIGN KEY (device)
        REFERENCES public.device (id) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE RESTRICT,
    CONSTRAINT current_lic_packet_device_payload_type_unique UNIQUE (device, payload_type)
);

CREATE INDEX IF NOT EXISTS current_lic_packet_device_packet_date_idx
    ON public.current_lic_packet USING btree
    (device ASC NULLS LAST, packet_date ASC NULLS LAST);
```

---

### Table: lic_packet

```sql
CREATE TABLE IF NOT EXISTS public.lic_packet
(
    id SERIAL PRIMARY KEY,
    device uuid NOT NULL,
    date_created timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    packet_date timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    payload_type character varying(50) NOT NULL,
    payload_data jsonb NOT NULL,
    CONSTRAINT lic_packet_device_foreign FOREIGN KEY (device)
        REFERENCES public.device (id) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE RESTRICT,
    CONSTRAINT lic_packet_device_packet_date_payload_type_unique UNIQUE (device, packet_date, payload_type)
);

-- Convert to TimescaleDB hypertable with monthly chunking
SELECT create_hypertable(
  'lic_packet',
  'packet_date',
  chunk_time_interval => INTERVAL '1 month',
  if_not_exists => TRUE
);

-- Configure compression for data older than 3 months
ALTER TABLE lic_packet SET (
  timescaledb.compress,
  timescaledb.compress_segmentby = 'device, payload_type'
);

SELECT add_compression_policy(
  'lic_packet',
  INTERVAL '3 months'
);

-- Add retention policy to automatically drop data older than 2 years
SELECT add_retention_policy(
  'lic_packet', 
  INTERVAL '2 years'
);

-- Performance indexes
CREATE INDEX IF NOT EXISTS lic_packet_packet_date_idx
    ON public.lic_packet USING btree (packet_date ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS lic_packet_device_packet_date_idx
    ON public.lic_packet USING btree 
    (device ASC NULLS LAST, packet_date ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS lic_packet_payload_type_packet_date_idx
    ON public.lic_packet USING btree 
    (payload_type ASC NULLS LAST, packet_date ASC NULLS LAST);
```

---

### Function: handle_lic_packet_upsert

```sql
CREATE OR REPLACE FUNCTION handle_lic_packet_upsert()
RETURNS TRIGGER AS $$
DECLARE
  existing_record RECORD;
BEGIN
  -- Always insert into lic_packet (historical record)
  INSERT INTO lic_packet (device, date_created, packet_date, payload_type, payload_data)
  VALUES (NEW.device, NEW.date_created, NEW.packet_date, NEW.payload_type, NEW.payload_data);

  -- Check for existing record in current_lic_packet with same device and payload_type
  SELECT * INTO existing_record
  FROM current_lic_packet
  WHERE device = NEW.device AND payload_type = NEW.payload_type;

  IF existing_record IS NOT NULL THEN
    -- Compare packet_date to determine if new packet is newer
    IF NEW.packet_date > existing_record.packet_date THEN
      -- New packet is newer: delete existing and allow insert of new
      DELETE FROM current_lic_packet
      WHERE device = NEW.device AND payload_type = NEW.payload_type;
      -- The new record will be inserted normally by the INSERT operation
    ELSE
      -- New packet is older or same: prevent insert into current_lic_packet
      -- Return NULL to cancel the INSERT operation for current_lic_packet
      RETURN NULL;
    END IF;
  END IF;
  
  -- If no existing record or new packet is newer, allow the insert
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

---

### Function: handle_lic_packet_update

```sql
CREATE OR REPLACE FUNCTION handle_lic_packet_update()
RETURNS TRIGGER AS $$
BEGIN
  -- Always insert update into lic_packet (historical record)
  INSERT INTO lic_packet (device, date_created, packet_date, payload_type, payload_data)
  VALUES (NEW.device, NOW(), NEW.packet_date, NEW.payload_type, NEW.payload_data);

  -- Allow the update to proceed normally
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

---

### LIC Packet Triggers

```sql
CREATE TRIGGER lic_packet_conditional_upsert_trigger
BEFORE INSERT ON current_lic_packet
FOR EACH ROW
EXECUTE FUNCTION handle_lic_packet_upsert();

CREATE TRIGGER lic_packet_update_trigger
BEFORE UPDATE ON current_lic_packet
FOR EACH ROW
EXECUTE FUNCTION handle_lic_packet_update();
```

---

### Table: device_message_request

```sql
CREATE TABLE IF NOT EXISTS public.device_message_request
(
    -- ========================================================================
    -- Primary Identification
    -- ========================================================================
    id uuid NOT NULL DEFAULT gen_random_uuid(),

    -- ========================================================================
    -- Device Relationships
    -- ========================================================================
    device uuid NOT NULL,                    -- Target device (must be LIC model)
    property_device uuid,                    -- Optional: specific property_device association for mesh-aware routing

    -- ========================================================================
    -- Message Content (IncomingPacket structure)
    -- ========================================================================
    packet_id bigint NOT NULL,               -- Unique packet identifier for protobuf IncomingPacket.id field (must be unique per device)
    payload_type character varying(50) NOT NULL,  -- Type of payload: config, devices, scheduling, dev_scheduling, automation, control, command, request_info, firmware_update
    payload_data jsonb NOT NULL,             -- Serialized protobuf payload data as JSON (human-readable, for debugging)
    payload_bytes bytea,                     -- Compiled protobuf binary data ready for transmission (nullable, populated during processing)
    message_hash character varying(64),      -- SHA-256 hash for deduplication (optional)

    -- ========================================================================
    -- Message Relationships & Correlation
    -- ========================================================================
    parent_message_id uuid,                  -- Reference to parent message for sequential operations (creates message chains)
    correlation_id uuid,                     -- Correlation identifier for grouping related messages across devices/operations

    -- ========================================================================
    -- Delivery Control
    -- ========================================================================
    status character varying(20) NOT NULL DEFAULT 'pending',     -- Message lifecycle status
    priority smallint NOT NULL DEFAULT 5,                        -- Priority (1=highest, 10=lowest)

    -- ========================================================================
    -- Scheduling & Timing
    -- ========================================================================
    scheduled_at timestamp with time zone NOT NULL DEFAULT now(),     -- When to process this message (now() = immediate, future = scheduled)
    expires_at timestamp with time zone,                              -- Message expiration (NULL = never expires)
    sent_at timestamp with time zone,                                 -- When message was successfully sent
    acknowledged_at timestamp with time zone,                         -- When device acknowledged receipt (if applicable)

    -- ========================================================================
    -- Retry & Error Handling
    -- ========================================================================
    attempts smallint NOT NULL DEFAULT 0,         -- Number of delivery attempts made
    max_attempts smallint NOT NULL DEFAULT 3,     -- Maximum retry attempts before marking as failed
    retry_delay_seconds integer DEFAULT 30,       -- Seconds to wait between retry attempts
    last_error text,                               -- Last error message encountered

    -- ========================================================================
    -- Standard Audit Fields (following existing table patterns)
    -- ========================================================================
    date_created timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_created uuid,                       -- User who created this message request
    date_updated timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_updated uuid,                       -- User who last updated this request
    metadata jsonb,                          -- Transport details (mqtt_topic, qos, etc.) and flexible metadata storage
    notes text,                              -- Human-readable notes

    -- ========================================================================
    -- Constraints
    -- ========================================================================
    CONSTRAINT device_message_request_pkey PRIMARY KEY (id),

    -- Foreign Key Constraints
    CONSTRAINT device_message_request_device_foreign
        FOREIGN KEY (device) REFERENCES public.device (id)
        ON UPDATE RESTRICT ON DELETE RESTRICT,

    CONSTRAINT device_message_request_property_device_foreign
        FOREIGN KEY (property_device) REFERENCES public.property_device (id)
        ON UPDATE RESTRICT ON DELETE RESTRICT,

    CONSTRAINT device_message_request_parent_foreign
        FOREIGN KEY (parent_message_id) REFERENCES public.device_message_request (id)
        ON UPDATE RESTRICT ON DELETE SET NULL,

    CONSTRAINT device_message_request_user_created_foreign
        FOREIGN KEY (user_created) REFERENCES public.directus_users (id)
        ON UPDATE RESTRICT ON DELETE RESTRICT,

    CONSTRAINT device_message_request_user_updated_foreign
        FOREIGN KEY (user_updated) REFERENCES public.directus_users (id)
        ON UPDATE RESTRICT ON DELETE RESTRICT,

    -- Business Logic Constraints
    CONSTRAINT device_message_request_status_check
        CHECK (status IN ('pending', 'processing', 'sent', 'acknowledged', 'failed', 'expired', 'cancelled')),

    CONSTRAINT device_message_request_priority_check
        CHECK (priority >= 1 AND priority <= 10),

    CONSTRAINT device_message_request_payload_type_check
        CHECK (payload_type IN ('config', 'devices', 'scheduling', 'dev_scheduling', 'automation', 'control', 'command', 'request_info', 'firmware_update')),

    CONSTRAINT device_message_request_attempts_check
        CHECK (attempts >= 0 AND attempts <= max_attempts),

    CONSTRAINT device_message_request_max_attempts_check
        CHECK (max_attempts >= 1 AND max_attempts <= 100),

    CONSTRAINT device_message_request_retry_delay_check
        CHECK (retry_delay_seconds >= 0),

    -- Timing Logic Constraints
    CONSTRAINT device_message_request_timing_check
        CHECK (expires_at IS NULL OR expires_at > scheduled_at),

    CONSTRAINT device_message_request_sent_after_scheduled_check
        CHECK (sent_at IS NULL OR sent_at >= scheduled_at),

    CONSTRAINT device_message_request_ack_after_sent_check
        CHECK (acknowledged_at IS NULL OR (sent_at IS NOT NULL AND acknowledged_at >= sent_at)),

    -- packet_id must be unique per device (to avoid confusion in device responses)
    CONSTRAINT device_message_request_device_packet_id_unique
        UNIQUE (device, packet_id)
);

-- ============================================================================
-- Indexes for Performance
-- ============================================================================

-- Primary queue processing index (most important)
CREATE INDEX IF NOT EXISTS device_message_request_queue_processing_idx
    ON public.device_message_request USING btree
    (status, scheduled_at, priority)
    WHERE status IN ('pending', 'processing');

-- Device-specific message lookup
CREATE INDEX IF NOT EXISTS device_message_request_device_status_idx
    ON public.device_message_request USING btree
    (device, status, date_created DESC);

-- Property device association lookup
CREATE INDEX IF NOT EXISTS device_message_request_property_device_idx
    ON public.device_message_request USING btree
    (property_device)
    WHERE property_device IS NOT NULL;

-- Scheduled messages lookup
CREATE INDEX IF NOT EXISTS device_message_request_scheduled_idx
    ON public.device_message_request USING btree
    (scheduled_at)
    WHERE status = 'pending';

-- Deduplication index (sparse - only when hash exists)
CREATE UNIQUE INDEX IF NOT EXISTS device_message_request_dedup_idx
    ON public.device_message_request USING btree
    (message_hash)
    WHERE message_hash IS NOT NULL;

-- Message correlation and chains
CREATE INDEX IF NOT EXISTS device_message_request_correlation_idx
    ON public.device_message_request USING btree
    (correlation_id)
    WHERE correlation_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS device_message_request_parent_idx
    ON public.device_message_request USING btree
    (parent_message_id)
    WHERE parent_message_id IS NOT NULL;

-- Failed/retry messages lookup
CREATE INDEX IF NOT EXISTS device_message_request_retry_idx
    ON public.device_message_request USING btree
    (status, attempts, scheduled_at)
    WHERE status = 'failed' AND attempts < max_attempts;

-- Cleanup/archival index
CREATE INDEX IF NOT EXISTS device_message_request_cleanup_idx
    ON public.device_message_request USING btree
    (status, date_created)
    WHERE status IN ('sent', 'acknowledged', 'failed', 'expired', 'cancelled');

-- Standard timestamp update trigger
CREATE OR REPLACE TRIGGER set_device_message_request_date_updated
    BEFORE UPDATE
    ON public.device_message_request
    FOR EACH ROW
    EXECUTE FUNCTION public.update_timestamp_column();
```

---

### Table: account_user

```sql
CREATE TABLE IF NOT EXISTS public.account_user
(
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    account uuid NOT NULL,
    "user" uuid NOT NULL,
    role character varying(255)  NOT NULL DEFAULT 'admin'::character varying,
    start_date timestamp with time zone NOT NULL,
    end_date timestamp with time zone,
    date_created timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_created uuid,
    date_updated timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_updated uuid,
    metadata jsonb,
    notes text,
    CONSTRAINT account_user_pkey PRIMARY KEY (id),
    CONSTRAINT account_user_account_user_unique UNIQUE (account, "user"),
    CONSTRAINT account_user_account_foreign FOREIGN KEY (account)
        REFERENCES public.account (id) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE RESTRICT,
    CONSTRAINT account_user_user_created_foreign FOREIGN KEY (user_created)
        REFERENCES public.directus_users (id) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE RESTRICT,
    CONSTRAINT account_user_user_foreign FOREIGN KEY ("user")
        REFERENCES public.directus_users (id) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE RESTRICT,
    CONSTRAINT account_user_user_updated_foreign FOREIGN KEY (user_updated)
        REFERENCES public.directus_users (id) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE RESTRICT,
    CONSTRAINT account_user_role_check CHECK (role::text = ANY (ARRAY['admin'::character varying, 'user'::character varying, 'guest'::character varying]::text[]))
);

CREATE INDEX IF NOT EXISTS account_user_account_index
    ON public.account_user USING btree
    (account ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS account_user_user_index
    ON public.account_user USING btree
    ("user" ASC NULLS LAST);

CREATE OR REPLACE TRIGGER set_account_user_date_updated
    BEFORE UPDATE
    ON public.account_user
    FOR EACH ROW
    EXECUTE FUNCTION public.update_timestamp_column();
```

---

### Table: water_pump

```sql
CREATE TABLE IF NOT EXISTS public.water_pump
(
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    property uuid NOT NULL,
    water_pump_controller uuid,
    label character varying(255) NOT NULL,
    identifier character varying(255) NOT NULL,
    pump_type character varying(255) NOT NULL,
    pump_model character varying(255),
    has_frequency_inverter boolean DEFAULT false NOT NULL,
    monitor_operation boolean DEFAULT false NOT NULL,
    mode character varying(255) DEFAULT 'PULSE'::character varying NOT NULL,
    notes text,
    metadata jsonb,
    date_created timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_created uuid,
    date_updated timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_updated uuid,
    flow_rate_lh numeric(10,2),
    CONSTRAINT water_pump_pkey PRIMARY KEY (id),
    CONSTRAINT water_pump_property_identifier_unique UNIQUE (property, identifier),
    CONSTRAINT water_pump_property_label_unique UNIQUE (property, label),
    CONSTRAINT water_pump_property_foreign FOREIGN KEY (property)
        REFERENCES public.property (id) MATCH SIMPLE
        ON DELETE RESTRICT,
    CONSTRAINT water_pump_user_created_foreign FOREIGN KEY (user_created)
        REFERENCES public.directus_users (id) MATCH SIMPLE
        ON DELETE RESTRICT,
    CONSTRAINT water_pump_user_updated_foreign FOREIGN KEY (user_updated)
        REFERENCES public.directus_users (id) MATCH SIMPLE
        ON DELETE RESTRICT,
    CONSTRAINT water_pump_water_pump_controller_foreign FOREIGN KEY (water_pump_controller)
        REFERENCES public.device (id) MATCH SIMPLE
        ON DELETE RESTRICT,
    CONSTRAINT water_pump_type_check CHECK (pump_type::text = ANY (ARRAY['IRRIGATION'::character varying, 'FERTIGATION'::character varying, 'SERVICE'::character varying]::text[])),
    CONSTRAINT water_pump_mode_check CHECK (mode::text = ANY (ARRAY['PULSE'::character varying, 'CONTINUOUS'::character varying]::text[]))
);

CREATE OR REPLACE TRIGGER set_water_pump_date_updated
    BEFORE UPDATE
    ON public.water_pump
    FOR EACH ROW
    EXECUTE FUNCTION public.update_timestamp_column();
```

---

### Table: project

```sql
CREATE TABLE IF NOT EXISTS public.project
(
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    property uuid NOT NULL,
    name character varying(255)  NOT NULL,
    irrigation_water_pump uuid NOT NULL,
    fertigation_water_pump uuid,
    localized_irrigation_controller uuid NOT NULL,
    description text ,
    start_date date,
    end_date date,
    date_created timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_created uuid,
    date_updated timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_updated uuid,
    metadata jsonb,
    notes text,
    CONSTRAINT project_pkey PRIMARY KEY (id),
    CONSTRAINT project_property_name_unq UNIQUE (property, name),
    CONSTRAINT project_fertigation_water_pump_foreign FOREIGN KEY (fertigation_water_pump)
        REFERENCES public.water_pump (id) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE RESTRICT,
    CONSTRAINT project_irrigation_water_pump_foreign FOREIGN KEY (irrigation_water_pump)
        REFERENCES public.water_pump (id) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE RESTRICT,
    CONSTRAINT project_localized_irrigation_controller_foreign FOREIGN KEY (localized_irrigation_controller)
        REFERENCES public.device (id) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE RESTRICT,
    CONSTRAINT project_property_foreign FOREIGN KEY (property)
        REFERENCES public.property (id) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE RESTRICT,
    CONSTRAINT project_user_created_foreign FOREIGN KEY (user_created)
        REFERENCES public.directus_users (id) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE RESTRICT,
    CONSTRAINT project_user_updated_foreign FOREIGN KEY (user_updated)
        REFERENCES public.directus_users (id) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE RESTRICT,
    CONSTRAINT project_property_no_overlap EXCLUDE USING gist (
        property WITH =,
        tsrange(start_date::timestamp without time zone, end_date::timestamp without time zone) WITH &&)
);

CREATE OR REPLACE TRIGGER set_project_date_updated
    BEFORE UPDATE
    ON public.project
    FOR EACH ROW
    EXECUTE FUNCTION public.update_timestamp_column();
```

---

### Table: sector

```sql
CREATE TABLE IF NOT EXISTS public.sector
(
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    project uuid NOT NULL,
    name character varying(255)  NOT NULL,
    description text ,
    valve_controller uuid NOT NULL,
    valve_controller_output smallint NOT NULL,
    area double precision,
    polygon geometry(Polygon,4326),
    power smallint NOT NULL DEFAULT 0,
    date_created timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_created uuid,
    date_updated timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_updated uuid,
    metadata jsonb,
    notes text,
    CONSTRAINT sector_pkey PRIMARY KEY (id),
    CONSTRAINT sector_valve_controller_valve_controller_output_unq UNIQUE (valve_controller, valve_controller_output),
    CONSTRAINT sector_project_name_unq UNIQUE (project, name),
    CONSTRAINT sector_project_foreign FOREIGN KEY (project)
        REFERENCES public.project (id) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE RESTRICT,
    CONSTRAINT sector_valve_controller_foreign FOREIGN KEY (valve_controller)
        REFERENCES public.device (id) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE RESTRICT,
    CONSTRAINT sector_user_created_foreign FOREIGN KEY (user_created)
        REFERENCES public.directus_users (id) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE RESTRICT,
    CONSTRAINT sector_user_updated_foreign FOREIGN KEY (user_updated)
        REFERENCES public.directus_users (id) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE RESTRICT,
    CONSTRAINT chk_valve_controller_output CHECK (valve_controller_output >= 1 AND valve_controller_output <= 4),
    CONSTRAINT chk_sector_power CHECK (power >= 0 AND power <= 100)
);

CREATE OR REPLACE TRIGGER set_sector_date_updated
    BEFORE UPDATE
    ON public.sector
    FOR EACH ROW
    EXECUTE FUNCTION public.update_timestamp_column();
```

---

### Table: irrigation_plan

```sql
CREATE TABLE IF NOT EXISTS public.irrigation_plan
(
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    project uuid NOT NULL,
    name character varying(255)  NOT NULL,
    description text ,
    start_time time without time zone NOT NULL,
    days_of_week jsonb DEFAULT '["MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN"]'::jsonb NOT NULL,
    is_enabled boolean NOT NULL DEFAULT false,
    fertigation_enabled boolean NOT NULL DEFAULT false,
    backwash_enabled boolean NOT NULL DEFAULT false,
    total_irrigation_duration integer NOT NULL DEFAULT 0,
    start_date date,
    end_date date,
    date_created timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_created uuid,
    date_updated timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_updated uuid,
    metadata jsonb,
    notes text,
    CONSTRAINT irrigation_plan_pkey PRIMARY KEY (id),
    CONSTRAINT irrigation_plan_project_name_unq UNIQUE (project, name),
    CONSTRAINT irrigation_plan_project_foreign FOREIGN KEY (project)
        REFERENCES public.project (id) MATCH SIMPLE
        ON DELETE RESTRICT,
    CONSTRAINT irrigation_plan_user_created_foreign FOREIGN KEY (user_created)
        REFERENCES public.directus_users (id) MATCH SIMPLE
        ON DELETE RESTRICT,
    CONSTRAINT irrigation_plan_user_updated_foreign FOREIGN KEY (user_updated)
        REFERENCES public.directus_users (id) MATCH SIMPLE
        ON DELETE RESTRICT
);

CREATE OR REPLACE TRIGGER set_irrigation_plan_date_updated
    BEFORE UPDATE
    ON public.irrigation_plan
    FOR EACH ROW
    EXECUTE FUNCTION public.update_timestamp_column();
```

---

### Table: irrigation_plan_step

```sql
CREATE TABLE IF NOT EXISTS public.irrigation_plan_step
(
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    irrigation_plan uuid NOT NULL,
    sector uuid NOT NULL,
    description text ,
    "order" integer NOT NULL,
    duration_seconds integer NOT NULL,
    fertigation_start_delay_seconds integer,
    fertigation_duration_seconds integer,
    date_created timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_created uuid,
    date_updated timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_updated uuid,
    metadata jsonb,
    notes text,
    CONSTRAINT irrigation_plan_step_pkey PRIMARY KEY (id),
    CONSTRAINT irrigation_plan_step_irrigation_plan_order_unique UNIQUE (irrigation_plan, "order") DEFERRABLE INITIALLY DEFERRED,
    CONSTRAINT irrigation_plan_step_irrigation_plan_sector_unique UNIQUE (irrigation_plan, sector),
    CONSTRAINT irrigation_plan_step_irrigation_plan_foreign FOREIGN KEY (irrigation_plan)
        REFERENCES public.irrigation_plan (id) MATCH SIMPLE
        ON DELETE RESTRICT,
    CONSTRAINT irrigation_plan_step_sector_foreign FOREIGN KEY (sector)
        REFERENCES public.sector (id) MATCH SIMPLE
        ON DELETE RESTRICT,
    CONSTRAINT irrigation_plan_step_user_created_foreign FOREIGN KEY (user_created)
        REFERENCES public.directus_users (id) MATCH SIMPLE
        ON DELETE RESTRICT,
    CONSTRAINT irrigation_plan_step_user_updated_foreign FOREIGN KEY (user_updated)
        REFERENCES public.directus_users (id) MATCH SIMPLE
        ON DELETE RESTRICT,
    CONSTRAINT irrigation_plan_step_duration_positive CHECK (duration_seconds > 0),
    CONSTRAINT irrigation_plan_step_fertigation_delay_nonnegative CHECK (fertigation_start_delay_seconds >= 0),
    CONSTRAINT irrigation_plan_step_fertigation_duration_positive CHECK (fertigation_duration_seconds > 0)
);

CREATE OR REPLACE TRIGGER set_irrigation_plan_step_date_updated
    BEFORE UPDATE
    ON public.irrigation_plan_step
    FOR EACH ROW
    EXECUTE FUNCTION public.update_timestamp_column();

CREATE OR REPLACE TRIGGER trg_update_irrigation_plan_total_irrigation_duration
    AFTER INSERT OR DELETE OR UPDATE OF duration_seconds, irrigation_plan
    ON public.irrigation_plan_step
    FOR EACH ROW
    EXECUTE FUNCTION public.update_plan_total_irrigation_duration();
```

---

### Table: reservoir

```sql
CREATE TABLE IF NOT EXISTS public.reservoir
(
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    property uuid NOT NULL,
    name character varying(255) NOT NULL,
    reservoir_monitor uuid,
    water_pump uuid,
    description text,
    capacity numeric(15,3),
    safety_time_minutes integer,
    location geometry(Point,4326),
    notes text,
    date_created timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_created uuid,
    date_updated timestamp with time zone,
    user_updated uuid,
    metadata jsonb,
    enabled boolean DEFAULT true NOT NULL,
    CONSTRAINT reservoir_pkey PRIMARY KEY (id),
    CONSTRAINT reservoir_property_name_unq UNIQUE (property, name),
    CONSTRAINT reservoir_reservoir_monitor_unique UNIQUE (reservoir_monitor),
    CONSTRAINT reservoir_water_pump_unique UNIQUE (water_pump),
    CONSTRAINT reservoir_property_foreign FOREIGN KEY (property)
        REFERENCES public.property (id) MATCH SIMPLE
        ON DELETE RESTRICT,
    CONSTRAINT reservoir_reservoir_monitor_foreign FOREIGN KEY (reservoir_monitor)
        REFERENCES public.device (id) MATCH SIMPLE
        ON DELETE RESTRICT,
    CONSTRAINT reservoir_water_pump_foreign FOREIGN KEY (water_pump)
        REFERENCES public.water_pump (id) MATCH SIMPLE
        ON DELETE RESTRICT,
    CONSTRAINT reservoir_user_created_foreign FOREIGN KEY (user_created)
        REFERENCES public.directus_users (id) MATCH SIMPLE
        ON DELETE RESTRICT,
    CONSTRAINT reservoir_user_updated_foreign FOREIGN KEY (user_updated)
        REFERENCES public.directus_users (id) MATCH SIMPLE
        ON DELETE RESTRICT
);

CREATE OR REPLACE TRIGGER set_reservoir_date_updated
    BEFORE UPDATE
    ON public.reservoir
    FOR EACH ROW
    EXECUTE FUNCTION public.update_timestamp_column();

CREATE OR REPLACE TRIGGER trg_reservoir_water_pump_service_only
    BEFORE INSERT OR UPDATE
    ON public.reservoir
    FOR EACH ROW
    EXECUTE FUNCTION public.check_reservoir_water_pump_service_only();
```

---

### Additional Functions

#### Function: update_timestamp_column

```sql
CREATE OR REPLACE FUNCTION update_timestamp_column() RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
  NEW.date_updated = NOW();
  RETURN NEW;
END;
$$;
```

#### Function: update_plan_total_irrigation_duration

```sql
CREATE OR REPLACE FUNCTION update_plan_total_irrigation_duration() RETURNS trigger
LANGUAGE plpgsql
AS $$
DECLARE
  affected_plan UUID;
BEGIN
  IF (TG_OP = 'DELETE') THEN
    affected_plan := OLD.irrigation_plan;
  ELSIF (TG_OP = 'UPDATE' AND NEW.irrigation_plan <> OLD.irrigation_plan) THEN
    -- If plan FK changed, update both old and new
    UPDATE irrigation_plan
      SET total_irrigation_duration = COALESCE((
        SELECT SUM(duration_seconds)
        FROM irrigation_plan_step
        WHERE irrigation_plan = OLD.irrigation_plan
      ), 0)
      WHERE id = OLD.irrigation_plan;
    affected_plan := NEW.irrigation_plan;
  ELSE
    affected_plan := NEW.irrigation_plan;
  END IF;

  UPDATE irrigation_plan
    SET total_irrigation_duration = COALESCE((
      SELECT SUM(duration_seconds)
      FROM irrigation_plan_step
      WHERE irrigation_plan = affected_plan
    ), 0)
    WHERE id = affected_plan;

  RETURN NULL;
END;
$$;
```

#### Function: check_reservoir_water_pump_service_only

```sql
CREATE OR REPLACE FUNCTION check_reservoir_water_pump_service_only() RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
  IF NEW.water_pump IS NOT NULL THEN
    PERFORM 1 FROM water_pump WHERE id = NEW.water_pump AND pump_type = 'SERVICE';
    IF NOT FOUND THEN
      RAISE EXCEPTION 'Associated water_pump must have pump_type = SERVICE';
    END IF;
  END IF;
  RETURN NEW;
END;
$$;
```

#### Function: delete_directus_config

```sql
CREATE OR REPLACE FUNCTION delete_directus_config() RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
  RAISE LOG 'REMOVING migration: %', OLD.version;
  DELETE FROM directus_migration.directus_config WHERE name = OLD.version;
  RAISE LOG 'LAST MIGRATION: %', (SELECT version FROM directus_migrations order by timestamp desc limit 1);
  RETURN NEW;
END;
$$;
```

#### Function: insert_directus_config

```sql
CREATE OR REPLACE FUNCTION insert_directus_config() RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
  INSERT INTO directus_migration.directus_config(config, name)
  SELECT config, NEW.version FROM directus_data_config_json;
  RETURN NEW;
END;
$$;
```

---

## Mesh Network Constraint Functions and Triggers

The following functions and triggers enforce mesh network constraints to ensure that devices within reservoirs, projects, and sectors are properly associated with the same LIC through the mesh network.

#### Function: get_lic_for_device

```sql
CREATE OR REPLACE FUNCTION get_lic_for_device(p_device_id UUID)
RETURNS UUID AS $$
DECLARE
  v_lic_property_device_id UUID;
BEGIN
  SELECT lic_property_device INTO v_lic_property_device_id
  FROM mesh_device_mapping
  WHERE mesh_property_device IN (
    SELECT id FROM property_device WHERE device = p_device_id AND end_date IS NULL
  )
  AND end_date IS NULL;
  RETURN v_lic_property_device_id;
END;
$$ LANGUAGE plpgsql;
```

#### Function: check_reservoir_mesh

```sql
CREATE OR REPLACE FUNCTION check_reservoir_mesh()
RETURNS TRIGGER AS $$
DECLARE
  v_rm_lic_id UUID;
  v_pump_lic_id UUID;
BEGIN
  IF NEW.reservoir_monitor IS NOT NULL AND NEW.water_pump IS NOT NULL THEN
    -- Get LIC for Reservoir Monitor
    SELECT get_lic_for_device(NEW.reservoir_monitor) INTO v_rm_lic_id;
    
    -- Get LIC for Water Pump Controller
    SELECT get_lic_for_device(wp.water_pump_controller) INTO v_pump_lic_id
    FROM water_pump wp WHERE wp.id = NEW.water_pump;

    IF v_rm_lic_id IS DISTINCT FROM v_pump_lic_id THEN
      RAISE EXCEPTION 'Reservoir Monitor and Service Water Pump must be in the same mesh network.';
    END IF;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

#### Function: check_project_mesh

```sql
CREATE OR REPLACE FUNCTION check_project_mesh()
RETURNS TRIGGER AS $$
DECLARE
  v_project_lic_property_device_id UUID;
  v_irrigation_pump_lic_property_device_id UUID;
  v_fertigation_pump_lic_property_device_id UUID;
BEGIN
  IF NEW.localized_irrigation_controller IS NOT NULL THEN
    -- Get the LIC's property_device ID
    SELECT id INTO v_project_lic_property_device_id
    FROM property_device
    WHERE device = NEW.localized_irrigation_controller AND end_date IS NULL;

    -- Check irrigation pump
    IF NEW.irrigation_water_pump IS NOT NULL THEN
      SELECT get_lic_for_device(wp.water_pump_controller) INTO v_irrigation_pump_lic_property_device_id
      FROM water_pump wp WHERE wp.id = NEW.irrigation_water_pump;
      IF v_project_lic_property_device_id IS DISTINCT FROM v_irrigation_pump_lic_property_device_id THEN
        RAISE EXCEPTION 'Irrigation pump must be in the same mesh network as the project LIC.';
      END IF;
    END IF;

    -- Check fertigation pump
    IF NEW.fertigation_water_pump IS NOT NULL THEN
      SELECT get_lic_for_device(wp.water_pump_controller) INTO v_fertigation_pump_lic_property_device_id
      FROM water_pump wp WHERE wp.id = NEW.fertigation_water_pump;
      IF v_project_lic_property_device_id IS DISTINCT FROM v_fertigation_pump_lic_property_device_id THEN
        RAISE EXCEPTION 'Fertigation pump must be in the same mesh network as the project LIC.';
      END IF;
    END IF;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

#### Function: check_sector_mesh

```sql
CREATE OR REPLACE FUNCTION check_sector_mesh()
RETURNS TRIGGER AS $$
DECLARE
  v_project_lic_property_device_id UUID;
  v_vc_lic_property_device_id UUID;
BEGIN
  -- Get Project's LIC property_device ID
  SELECT pd.id INTO v_project_lic_property_device_id
  FROM project p
  JOIN property_device pd ON pd.device = p.localized_irrigation_controller AND pd.end_date IS NULL
  WHERE p.id = NEW.project;

  IF v_project_lic_property_device_id IS NOT NULL THEN
    -- Get Valve Controller's LIC property_device ID
    SELECT get_lic_for_device(NEW.valve_controller) INTO v_vc_lic_property_device_id;

    IF v_project_lic_property_device_id IS DISTINCT FROM v_vc_lic_property_device_id THEN
      RAISE EXCEPTION 'Valve Controller must be in the same mesh network as the project LIC.';
    END IF;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

#### Mesh Network Constraint Triggers

```sql
-- Reservoir mesh constraints
CREATE TRIGGER trigger_check_reservoir_mesh_before_insert
BEFORE INSERT ON reservoir
FOR EACH ROW EXECUTE FUNCTION check_reservoir_mesh();

CREATE TRIGGER trigger_check_reservoir_mesh_before_update
BEFORE UPDATE ON reservoir
FOR EACH ROW EXECUTE FUNCTION check_reservoir_mesh();

-- Project mesh constraints
CREATE TRIGGER trigger_check_project_mesh_before_insert
BEFORE INSERT ON project
FOR EACH ROW EXECUTE FUNCTION check_project_mesh();

CREATE TRIGGER trigger_check_project_mesh_before_update
BEFORE UPDATE ON project
FOR EACH ROW EXECUTE FUNCTION check_project_mesh();

-- Sector mesh constraints
CREATE TRIGGER trigger_check_sector_mesh_before_insert
BEFORE INSERT ON sector
FOR EACH ROW EXECUTE FUNCTION check_sector_mesh();

CREATE TRIGGER trigger_check_sector_mesh_before_update
BEFORE UPDATE ON sector
FOR EACH ROW EXECUTE FUNCTION check_sector_mesh();
```

These triggers enforce the following mesh network constraints:
- **Reservoir**: If both Reservoir Monitor and Service Water Pump are configured, they must be in the same mesh network (connected to the same LIC).
- **Project**: Irrigation and fertigation pumps must be connected to the same LIC as the project's Localized Irrigation Controller.
- **Sector**: Valve controllers must be connected to the same LIC as their project's Localized Irrigation Controller.

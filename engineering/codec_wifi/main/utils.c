#include "utils.h"

void url_decode(char *dest, const char *src)
{
    char *d = dest;
    const char *s = src;
    while (*s) {
        if (*s == '%') {
            if (s[1] && s[2]) {
                int value;
                sscanf(s + 1, "%2x", &value);
                *d++ = (char)value;
                s += 3;
            } else {
                // Handle error: Invalid URL encoding
                break;
            }
        } else if (*s == '+') {
            *d++ = ' ';
            s++;
        } else {
            *d++ = *s++;
        }
    }
    *d = '\0';
}

int is_valid_char(char c)
{
    return isalnum(c) || c == '-' || c == '_' || c == '.' || c == '@';
}

int is_valid_string(const char *str)
{
    while (*str) {
        if (!is_valid_char((unsigned char)*str)) {
            return 0;
        }
        str++;
    }
    return 1;
}

void remove_invalid_chars(char *str)
{
    int i, j = 0;
    int len = strlen(str);

    for (i = 0; i < len; i++) {
        if (!is_valid_char(str[i])) {
            str[i] = '\0';
            break;
        }
    }
}

uint16_t crc16(uint8_t *buffer, uint16_t size)
{
    uint16_t crc = 0xFFFF;
    size_t i, j;
    for (i = 0; i < size; i++) {
        crc ^= buffer[i];
        for (j = 0; j < 8; j++) {
            if (crc & 0x0001) {
                crc >>= 1;
                crc ^= 0xa001;
            } else {
                crc >>= 1;
            }
        }
    }
    return crc;
}

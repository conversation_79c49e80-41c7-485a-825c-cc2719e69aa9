#include "driver/uart.h"

#define ESP_FW_VERSION 116
#define MESH_FW_VERSION 101
#define HARDWARE_VERSION 101

#define MAX_STA_SSID_SIZE 32
#define MAX_STA_PASS_SIZE 64

#define WIFI_STA_SSID      "GEO_ENGENHARIA"
#define WIFI_STA_PASS      "engenharia@110105"
#define WIFI_AP_SSID       "Codec"
#define WIFI_AP_PASS       "0123456789"
#define MAX_STA_CONN       4

#define AP_IP_ADDR "***********"
#define AP_GW_ADDR "***********"
#define AP_NETMASK "*************"

#define PORT           3333
#define IP_ADDR        "*************"
#define INTERVAL_5S    5
#define MAX_RETRY      10

#define BASIC_AUTH_USERNAME "byagro"
#define BASIC_AUTH_PASSWORD "i8dEYH7tcNxVf18"

#define MQTT_BROKER_URI "mqtt://mosquitto-codec.saas.byagro.dev.br:8003"
#define MQTT_BROKER_PORT 8003
//#define PUBLISH_TOPIC "/uplink"
//#define PUBLISH_TOPIC_REPORT "/report"
//#define SUBSCRIBE_TOPIC "/downlink"

#define TXD_PIN        (GPIO_NUM_17)
#define RXD_PIN        (GPIO_NUM_16)
#define UART_NUM       UART_NUM_2

#define BUF_SIZE (1024)
#define MAX_MESH_DEVICES 50
#define MAX_MESH_AUTOMATION 5
#define MAX_MESH_CONTROL 5
#define MAX_MESH_SCHEDULING 20
#define MAX_MESH_DEVICE_SCHEDULING 100

#define TIMER_20S   20
#define TIMER_25S   20
#define TIMER_30S   30
#define TIMER_2MIN  120
#define TIMER_3MIN  180
#define TIMER_5MIN  300
#define TIMER_10MIN 600
#define TIMER_20MIN 1200
#define TIMER_2H    2*3600
#define TIMER_4H    4*3600
#define TIMER_22H   22*3600

#define PULSE    0x01
#define MONI     0x02
#define MAX_DATA_SIZE 8192

#ifndef MIN
#define MIN(a, b) ((a) < (b) ? (a) : (b))
#endif
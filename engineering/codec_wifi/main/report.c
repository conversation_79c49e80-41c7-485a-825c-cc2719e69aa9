#include "report.h"
#include "outgoing_packet.pb-c.h"

static const char *TAG_REP = "REPORT";

static int last_indices[MAX_JSON_SCHEDULINGS];
static int last_automation_index;
extern int rainfall_last24h;
extern bool its_raining;
extern uint8_t mac[6];

extern bool is_scheduling_running(void);

void report_init(void)
{
    for (int i = 0; i < MAX_JSON_SCHEDULINGS; i++) {
        last_indices[i] = -1;
    }
    last_automation_index = -1;
}

void report_scheduling_start(int idx, time_t started_time) {
    pumplink_scheduling_report[idx].started_time = started_time;
    pumplink_scheduling_report[idx].shc_idx = idx;
    pumplink_scheduling_report[idx].sector_l1 = 0;
    pumplink_scheduling_report[idx].sector_l2 = 0;
    pumplink_scheduling_report[idx].sector_l3 = 0;
    pumplink_scheduling_report[idx].ferti_l1 = 0;
    pumplink_scheduling_report[idx].ferti_l2 = 0;
    pumplink_scheduling_report[idx].ferti_l3 = 0;
    pumplink_scheduling_report[idx].waterpump = false;
    pumplink_scheduling_report[idx].backwash = false;
    pumplink_scheduling_report[idx].status = S_REP_SCHED_START;

    for (int i = MAX_JSON_SCHEDULINGS - 1; i > 0; i--) {
        if (last_indices[i - 1] == idx) {
            last_indices[i - 1] = -1;
        }
        last_indices[i] = last_indices[i - 1];
    }
    last_indices[0] = idx;
}

void report_automation_start(int idx, time_t started_time) {
    last_automation_index = idx;
    pumplink_automation_report[idx].started_time = started_time;
    pumplink_automation_report[idx].status = S_REP_AUTO_START;
}

void report_automation_end(int idx, time_t end_time) {
    last_automation_index = idx;
    pumplink_automation_report[idx].end_time = end_time;
    pumplink_automation_report[idx].status = S_REP_AUTO_END;
}

size_t report_scheduling(uint8_t **buffer)
{
    // SchedulingReportPackage
    Codec__Out__SchedulingReport__SchedulingReportPackage rpt = CODEC__OUT__SCHEDULING_REPORT__SCHEDULING_REPORT_PACKAGE__INIT;

    // uso de memória automática → zero fragmentação
    Codec__Out__SchedulingReport__SchedulingReportData   entries[MAX_JSON_SCHEDULINGS];
    Codec__Out__SchedulingReport__SchedulingReportData  *ptrs  [MAX_JSON_SCHEDULINGS];

    size_t n = 0;
    for (int i = 0; i < MAX_JSON_SCHEDULINGS && n < MAX_JSON_SCHEDULINGS; ++i) {
        int idx = last_indices[i];
        if (idx == -1 || pumplink_scheduling_report[idx].started_time == 0)
            continue;

        Codec__Out__SchedulingReport__SchedulingReportData *e = &entries[n];
        codec__out__scheduling_report__scheduling_report_data__init(e);

        e->scheduling_idx  = pumplink_scheduling_report[idx].shc_idx;
        e->start_time      = pumplink_scheduling_report[idx].started_time;
        e->end_time        = 0;
        e->sector_bitmask1 = pumplink_scheduling_report[idx].sector_l1;
        e->sector_bitmask2 = pumplink_scheduling_report[idx].sector_l2;
        e->ferti_bitmask1  = pumplink_scheduling_report[idx].ferti_l1;
        e->ferti_bitmask2  = pumplink_scheduling_report[idx].ferti_l2;
        e->waterpump       = pumplink_scheduling_report[idx].waterpump;
        e->backwash        = pumplink_scheduling_report[idx].backwash;
        e->backwash_time   = 0;
        e->status          = pumplink_scheduling_report[idx].status;

        ptrs[n++] = e;
    }

    if (n == 0)
        return 0;

    rpt.data   = ptrs;
    rpt.n_data = n;

    // Encapsular no OutgoingPacket
    Codec__Out__OutgoingPacket pkt = CODEC__OUT__OUTGOING_PACKET__INIT;
    pkt.id              = (uint64_t)time(NULL);         /* timestamp UTC    */
    pkt.payload_case    = CODEC__OUT__OUTGOING_PACKET__PAYLOAD_SCHEDULING_REPORT;
    pkt.scheduling_report = &rpt;

    size_t len = codec__out__outgoing_packet__get_packed_size(&pkt);
    *buffer = malloc(len);
    if (!*buffer) {
        ESP_LOGE("REPORT", "No heap for protobuf buffer (%u B)", (unsigned)len);
        return 0;
    }
    codec__out__outgoing_packet__pack(&pkt, *buffer);
    return len;
}

size_t report_automation(uint8_t **buffer)
{
    if (last_automation_index == -1 || last_automation_index >= MAX_MESH_AUTOMATION) {
        return 0;
    }

    // Inicializa o relatório de automação
    Codec__Out__AutomationReport__AutomationReportData automation_data = CODEC__OUT__AUTOMATION_REPORT__AUTOMATION_REPORT_DATA__INIT;
    automation_data.auto_idx = last_automation_index;
    automation_data.start_time = pumplink_automation_report[last_automation_index].started_time;
    automation_data.restart_time = 0;

    // Define end_time condicionalmente
    if (pumplink_automation_report[last_automation_index].status == S_REP_AUTO_END) {
        automation_data.end_time = pumplink_automation_report[last_automation_index].end_time;
    } else {
        automation_data.end_time = 0;
    }

    automation_data.status = pumplink_automation_report[last_automation_index].status;

    // Pacote AutomationReportPackage
    Codec__Out__AutomationReport__AutomationReportPackage report_package = CODEC__OUT__AUTOMATION_REPORT__AUTOMATION_REPORT_PACKAGE__INIT;

    Codec__Out__AutomationReport__AutomationReportData *data_vec[1];
    data_vec[0] = &automation_data;

    report_package.n_data = 1;
    report_package.data = data_vec;

    // Pacote de saída
    Codec__Out__OutgoingPacket packet = CODEC__OUT__OUTGOING_PACKET__INIT;
    packet.id = (uint64_t)time(NULL);
    packet.payload_case = CODEC__OUT__OUTGOING_PACKET__PAYLOAD_AUTOMATION_REPORT;
    packet.automation_report = &report_package;

    // Calcula tamanho e serializa
    size_t len = codec__out__outgoing_packet__get_packed_size(&packet);
    *buffer = malloc(len);

    if (*buffer == NULL) {
        ESP_LOGE(TAG_REP, "Failed to allocate buffer for Protobuf automation report");
        return 0;
    }

    codec__out__outgoing_packet__pack(&packet, *buffer);
    return len;
}

size_t report_info(uint8_t **buffer, uint8_t type)
{
    if (!buffer) return 0;

    char mac_string[18];
    sprintf(mac_string, "%02X%02X%02X%02X%02X%02X", mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);

    // Preenche o InfoPackage
    Codec__Out__Info__InfoPackage info = CODEC__OUT__INFO__INFO_PACKAGE__INIT;
    if(type == S_REP_INFO_ALL){
        info.codec_id = mac_string;
    }
    info.firmware_esp = ESP_FW_VERSION;
    info.firmware_mesh = MESH_FW_VERSION;
    info.hardware_version = HARDWARE_VERSION;
    info.resets = system_info.resets;
    info.scheduling_running = is_scheduling_running();
    info.scheduling_paused = pause_scheduling;
    info.devices_id = last_devs_update;
    info.automation_id = last_auto_update;
    info.scheduling_id = last_sched_update;
    info.dev_scheduling_id = last_dev_sched_update;
    info.config_id = last_config_update;

    // Preenche o pacote de saída
    Codec__Out__OutgoingPacket packet = CODEC__OUT__OUTGOING_PACKET__INIT;
    packet.id = (uint64_t)time(NULL);  // ou outro gerador de ID
    packet.payload_case = CODEC__OUT__OUTGOING_PACKET__PAYLOAD_INFO;
    packet.info = &info;

    // Aloca buffer
    size_t len = codec__out__outgoing_packet__get_packed_size(&packet);
    uint8_t *buf = malloc(len);
    if (!buf) return 0;

    codec__out__outgoing_packet__pack(&packet, buf);

    *buffer = buf;
    return len;
}

size_t report_status(uint8_t **buffer)
{
    time_t now;
    time(&now);

    uint64_t sy_bitmask = 0;
    uint64_t on_bitmask = 0;
    uint64_t in_bitmask = 0;

    int limit = (device_count < MAX_MESH_DEVICES) ? device_count : MAX_MESH_DEVICES;
    for (int l = 0; l < limit; ++l) {
        if (difftime(now, pumplink_device_state[l].last_sync) < TIMER_20MIN) {
            sy_bitmask |= ((uint64_t)1 << l);
        }
        if (pumplink_device_state[l].device_status != 0) {
            on_bitmask |= ((uint64_t)1 << l);
        }
        if (pumplink_device_state[l].inputs & (1 << 1)) {
            in_bitmask |= ((uint64_t)1 << l);
        }
    }

    Codec__Out__Status__SystemStatusPackage status = CODEC__OUT__STATUS__SYSTEM_STATUS_PACKAGE__INIT;
    status.resets = system_info.resets;
    status.scheduling_running = is_scheduling_running();
    status.scheduling_paused = pause_scheduling;

    int pause_elapsed_time = difftime(now, pause_scheduling_time) / 60;
    if (pause_scheduling) {
        status.paused_time = pause_elapsed_time;
        status.has_paused_time_case = CODEC__OUT__STATUS__SYSTEM_STATUS_PACKAGE__HAS_PAUSED_TIME_PAUSED_TIME;
    }else {
        status.has_paused_time_case = CODEC__OUT__STATUS__SYSTEM_STATUS_PACKAGE__HAS_PAUSED_TIME__NOT_SET;
    }

    if (pumplink_config.raingauge_enabled) {
        status.raining = its_raining;
        status.rainfall = rainfall_last24h;
        status.has_raining_case = CODEC__OUT__STATUS__SYSTEM_STATUS_PACKAGE__HAS_RAINING_RAINING;
        status.has_rainfall_case = CODEC__OUT__STATUS__SYSTEM_STATUS_PACKAGE__HAS_RAINFALL_RAINFALL;
    }else{
        status.has_raining_case = CODEC__OUT__STATUS__SYSTEM_STATUS_PACKAGE__HAS_RAINING__NOT_SET;
        status.has_rainfall_case = CODEC__OUT__STATUS__SYSTEM_STATUS_PACKAGE__HAS_RAINFALL__NOT_SET;
    }
    
    status.sync_bitmask = sy_bitmask;
    status.on_bitmask = on_bitmask;
    status.input_bitmask = in_bitmask;
    status.failed_bitmask = 0;

    // Preenche o pacote de saída
    Codec__Out__OutgoingPacket packet = CODEC__OUT__OUTGOING_PACKET__INIT;
    packet.id = (uint64_t)now;
    packet.payload_case = CODEC__OUT__OUTGOING_PACKET__PAYLOAD_STATUS;
    packet.status = &status;

    // Calcula o tamanho necessário
    size_t len = codec__out__outgoing_packet__get_packed_size(&packet);
    *buffer = malloc(len);

    if (*buffer == NULL) {
        ESP_LOGE(TAG_REP, "Failed to allocate buffer for Protobuf message");
        return 0;
    }

    // Serializa o pacote no buffer
    codec__out__outgoing_packet__pack(&packet, *buffer);

    ESP_LOGI(TAG_REP,
         "rst:%u run:%u pause:%u pmin:%d rg_en:%u rain:%d mm24:%d sync:0x%016llx on:0x%016llx in:0x%016llx",
         system_info.resets,                                          // rst  (contagem de resets)
         is_scheduling_running(),                                     // run  (scheduler em execução)
         pause_scheduling,                                            // pause (flag 0/1)
         pause_scheduling ? pause_elapsed_time : -1,                  // pmin (-1 se não pausado)
         pumplink_config.raingauge_enabled,                           // rg_en (raingauge habilitado)
         pumplink_config.raingauge_enabled ? its_raining : -1,        // it's raining  (-1 se rg_en==0)
         pumplink_config.raingauge_enabled ? rainfall_last24h : -1,   // mm24  (-1 se rg_en==0)
         (unsigned long long)sy_bitmask,                              // syn   (dispositivos sincronizados)
         (unsigned long long)on_bitmask,                              // on    (dispositivos ON)
         (unsigned long long)in_bitmask);                             // in    (entradas ativas)

    return len;
}

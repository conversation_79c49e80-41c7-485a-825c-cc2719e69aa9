#ifndef MESH_H
#define MESH_H

#include "defines.h"
#include "memory.h"
#include "utils.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include <netinet/in.h>
#include <time.h>
#include "mbedtls/base64.h"


// Dispositivos a ser acionados
struct s_pumplink_devices {
    unsigned int meshid;
    unsigned char device_id;
    unsigned char type; 
    unsigned char out1;
    unsigned char out2;
    unsigned char input;
    unsigned char mode;
    unsigned char group;   // Talhão
    unsigned char sector;
    unsigned char eqpt_ver;
};

// Automação de dispositivos
struct s_pumplink_automation {
    unsigned short int level_dev_idx; 
    unsigned short int pump_dev_idx;  // indice de s_pumplink_devices
    unsigned char mask;
    unsigned char value;
    unsigned char enabled; 
    unsigned short int working_time;
};

struct s_pumplink_automation_state {
    unsigned int next_time; 
    bool canceled;
    bool tried;
    bool working;
    time_t last_log_info;
};

struct s_pumplink_control {
    unsigned short int dev_idx; 
    unsigned char action;
    unsigned short int working_time;
    unsigned char enabled; 
    unsigned int start_time;
    unsigned char state;
    unsigned int timeout;
};

// Agendamentos
struct s_pumplink_scheduling {
    unsigned int start_time;
    unsigned char days_of_week;
    unsigned char type;
    unsigned char number_of_steps;
    unsigned short int waterpump_dev_idx;
    unsigned short int waterpump_working_time;
    unsigned char allow_backwash;
    unsigned short int backwash_dev_idx;
    unsigned char allow_ferti;
    unsigned short int ferti_dev_idx;
    unsigned short int group;
};

// Configuração da retro lavagem
struct s_pumplink_config {
    unsigned short int backwash_cycle_time;
    unsigned short int backwash_duration;
    unsigned short int backwash_delay;
    unsigned char      raingauge_enabled;
    unsigned short int raingauge_factor; // passos de por mm
    unsigned short int rainfall_limit; // limite de chuva em mm
    unsigned short int rainfall_pause_duration; // duração da pausa
};

// Backwash state
struct s_pumplink_backwash_state {
    unsigned int last_time;
    unsigned int last_dev_idx;
};

// Agendamentos por dispositivo
struct s_pumplink_device_scheduling {
    unsigned short int shc_idx;  // indice de s_pumplink_scheduling
    unsigned short int dev_idx;  // indice de s_pumplink_devices
    unsigned char step;          // passo da sequencia
    unsigned int sector_working_time;
    unsigned int ferti_working_time;
    unsigned int ferti_delay;
};

// Agendamentos por dispositivo
struct s_pumplink_scheduling_only {
    unsigned short int dev_idx; // indice de s_pumplink_devices
    unsigned char days_of_week;
    unsigned char type;
    unsigned char seq_id;       // id da sequencia
    unsigned char step;         // passo da sequencia
    unsigned char n_step;
    unsigned int time;
    //unsigned int working_time;
};

// Estrutura para armazenar o estado dos agendamentos
struct s_pumplink_scheduling_state {
    int current_step;             // Passo atual
    int total_working_time_secs;   // Soma dos tempos de trabalho dos passos anteriores em segundos
    bool init_info;
    bool started;
    bool working;
    time_t reference_day;
    time_t last_log_info;
    time_t fail_devsch_log;
    unsigned int last_dev_idx;
};

// Estrutura para armazenar o estado dos agendamentos por dispositivo
struct s_pumplink_device_scheduling_state {
    unsigned char info;
    bool tried;
    bool executed;
    bool canceled;
};

struct s_pumplink_device_backwash_state {
    unsigned int inc_time;
    unsigned int timer;
    unsigned char attempt;
    unsigned int attempt_time;
    unsigned int next_attempt_time;
    unsigned int last_log_info;
};

struct s_pumplink_decode {
    unsigned int meshid;
    unsigned char devices_working;
    unsigned char outputs;
    unsigned char inputs;
    unsigned char flags;
    unsigned char device_id;
    unsigned char time_left;
    unsigned int last_sync;
};

struct s_pumplink_device_state {
    unsigned short int id;
    unsigned short int m;
    unsigned char device_status;
    unsigned char outputs;
    unsigned char inputs;
    unsigned char flags;
    unsigned char device_id;
    unsigned char time_left;
    unsigned int last_sync;
    unsigned int last_cmd;
    unsigned short int last_appoint; // to cancel scheduling
    unsigned int working_time_secs;
    unsigned char triggered;
    unsigned int triggered_time;
};

struct s_pumplink_scheduling_report {
    unsigned short int shc_idx;
    unsigned int started_time;
    unsigned int sector_l1;
    unsigned int sector_l2;
    unsigned int sector_l3;
    unsigned int ferti_l1;
    unsigned int ferti_l2;
    unsigned int ferti_l3;
    unsigned char waterpump;
    unsigned char backwash;
    unsigned int number_of_sectors;
    unsigned char has_waterpump;
    unsigned char has_ferti;
    unsigned char has_backwash;
    unsigned char status;
};

struct s_pumplink_automation_report {
    unsigned int started_time;
    unsigned int end_time;
    unsigned char status;
};

// Informações do sistema
struct s_system_info {
    unsigned int resets;
};

#ifndef MESH_GLOBAL
#define ME_GLOBAL extern
#else
#define ME_GLOBAL
#endif

ME_GLOBAL unsigned short int device_count;
ME_GLOBAL unsigned short int automation_count;
ME_GLOBAL unsigned short int schedule_count;
ME_GLOBAL unsigned short int schedules_per_device;

ME_GLOBAL char pause_scheduling;
ME_GLOBAL time_t pause_scheduling_time;
ME_GLOBAL unsigned int pause_scheduling_timer;

ME_GLOBAL unsigned int last_devs_update;
ME_GLOBAL unsigned int last_sched_update;
ME_GLOBAL unsigned int last_dev_sched_update;
ME_GLOBAL unsigned int last_wifi_update;
ME_GLOBAL unsigned int last_auto_update;
ME_GLOBAL unsigned int last_config_update;

ME_GLOBAL struct s_pumplink_devices pumplink_devices[MAX_MESH_DEVICES];
ME_GLOBAL struct s_pumplink_device_state pumplink_device_state[MAX_MESH_DEVICES];
ME_GLOBAL struct s_pumplink_scheduling pumplink_scheduling[MAX_MESH_SCHEDULING];
ME_GLOBAL struct s_pumplink_scheduling_state pumplink_scheduling_state[MAX_MESH_SCHEDULING];
ME_GLOBAL struct s_pumplink_scheduling_report pumplink_scheduling_report[MAX_MESH_SCHEDULING];
ME_GLOBAL struct s_pumplink_device_scheduling pumplink_device_scheduling[MAX_MESH_DEVICE_SCHEDULING];
ME_GLOBAL struct s_pumplink_device_scheduling_state pumplink_device_scheduling_state[MAX_MESH_DEVICES]; // Estado dos agendamentos por dispositivo
ME_GLOBAL struct s_pumplink_device_backwash_state pumplink_device_backwash_state[MAX_MESH_DEVICES];     // Estado da retro lavagem por dispositivo
ME_GLOBAL struct s_pumplink_decode pumplink_decode;
ME_GLOBAL struct s_pumplink_automation pumplink_automation[MAX_MESH_AUTOMATION];
ME_GLOBAL struct s_pumplink_automation_state pumplink_automation_state[MAX_MESH_AUTOMATION];
ME_GLOBAL struct s_pumplink_automation_report pumplink_automation_report[MAX_MESH_AUTOMATION];
ME_GLOBAL struct s_pumplink_control pumplink_control[MAX_MESH_CONTROL];
ME_GLOBAL struct s_pumplink_config pumplink_config;
ME_GLOBAL struct s_system_info system_info;


void mesh_pumplink_turn_on_encode(uint32_t id, uint8_t device_id, uint8_t out1, uint8_t out2, uint8_t input, uint8_t mode, uint16_t working_time, char *mesh_pkg);
void mesh_pumplink_turn_off_encode(uint32_t id, uint8_t device_id, uint8_t out, uint8_t mode, char *mesh_pkg);
void mesh_insert_pumplink_control(struct s_pumplink_control new_control);
esp_err_t mesh_pumplink_decode(uint8_t *bytes, size_t length, struct s_pumplink_decode *decode);

#endif // MESH_H

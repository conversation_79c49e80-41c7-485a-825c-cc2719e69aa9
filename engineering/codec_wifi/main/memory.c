#include "memory.h"
#include "mesh.h"

static const char *TAG_MEM = "Memory";
extern char sta_ssid[MAX_STA_SSID_SIZE];
extern char sta_pass[MAX_STA_PASS_SIZE];

esp_err_t memory_nvs_init()
{
    // Initialize NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }

    memory_nvs_stats();

    ESP_ERROR_CHECK(ret);
    return ret;
}

void memory_nvs_stats()
{
    nvs_stats_t nvs_stats;
    esp_err_t err = nvs_get_stats(NULL, &nvs_stats); 
    if (err == ESP_OK) {
        ESP_LOGI("NVS_STATS", "NVS partition usage: used_entries = %d, free_entries = %d, total_entries = %d",
                 nvs_stats.used_entries, nvs_stats.free_entries, nvs_stats.total_entries);
    } else {
        ESP_LOGE("NVS_STATS", "Failed to get NVS stats! (err=0x%X)", err);
    }
}

esp_err_t memory_save_system_info()
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("system", NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Informações do sistema
    err = nvs_set_blob(nvs_handle, "sysinfo", &system_info, sizeof(system_info));
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    err = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
    return err;
}

esp_err_t memory_load_system_info()
{
    nvs_handle_t nvs_handle;
    system_info.resets = 0;
    esp_err_t err = nvs_open("system", NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Carregar as informações do sistema
    size_t size = sizeof(system_info);
    err = nvs_get_blob(nvs_handle, "sysinfo", &system_info, &size);
    nvs_close(nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Atualiza o número de reinicializações
    system_info.resets += 1;
    ESP_LOGI(TAG_MEM, "Number of Resets: %u", system_info.resets);

    return ESP_OK;
}

esp_err_t memory_save_wifi_credentials(char* ssid, char* password)
{
    nvs_handle_t nvs_handle;
    esp_err_t err;

    // Abre o NVS
    err = nvs_open("wifi", NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Salva o SSID
    err = nvs_set_str(nvs_handle, "ssid", ssid);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Salva a senha
    err = nvs_set_str(nvs_handle, "passwd", password);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    err = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
    return err;
}

esp_err_t memory_load_wifi_credentials(char* ssid, size_t ssid_size, char* password, size_t password_size)
{
    nvs_handle_t nvs_handle;
    esp_err_t err;

    // Abre o NVS em modo de leitura
    err = nvs_open("wifi", NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Carrega o SSID
    err = nvs_get_str(nvs_handle, "ssid", ssid, &ssid_size);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Carrega a senha
    err = nvs_get_str(nvs_handle, "passwd", password, &password_size);
    nvs_close(nvs_handle);
    return err;
}

esp_err_t memory_save_devices()
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("devs", NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Salva horário da última atualização
    err = nvs_set_u32(nvs_handle, "devs_t", last_devs_update);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Salvar o número de dispositivos
    err = nvs_set_u16(nvs_handle, "devs_c", device_count);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Salvar a lista de dispositivos
    err = nvs_set_blob(nvs_handle, "devs_l", pumplink_devices, sizeof(pumplink_devices));
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    err = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
    return err;
}

esp_err_t memory_load_devices()
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("devs", NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Carregar o horário da última atualização
    err = nvs_get_u32(nvs_handle, "devs_t", (uint32_t *)&last_devs_update);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Carregar o número de dispositivos
    err = nvs_get_u16(nvs_handle, "devs_c", &device_count);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Carregar a lista de dispositivos
    size_t size = sizeof(pumplink_devices);
    err = nvs_get_blob(nvs_handle, "devs_l", pumplink_devices, &size);
    nvs_close(nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    ESP_LOGI(TAG_MEM, "Loaded Device List: %d devices", device_count);
    for (int i = 0; i < device_count; i++) {
        ESP_LOGI(TAG_MEM, "Device %d -> MeshID: %d, DeviceID: %d, Type: %d, Out1: %d, Out2: %d, Input: %d, Mode: %d, Group: %d, Sector: %d, Eqpt_ver %d",
            i, pumplink_devices[i].meshid, pumplink_devices[i].device_id, pumplink_devices[i].type,
            pumplink_devices[i].out1, pumplink_devices[i].out2, pumplink_devices[i].input,
            pumplink_devices[i].mode, pumplink_devices[i].group, pumplink_devices[i].sector, pumplink_devices[i].eqpt_ver);
    }

    return ESP_OK;
}

esp_err_t memory_save_scheduling()
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("sched", NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Salva horário da última atualização
    err = nvs_set_u32(nvs_handle, "sched_t", last_sched_update);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Salvar o número de agendamentos
    err = nvs_set_u16(nvs_handle, "sched_c", schedule_count);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Salvar a lista de agendamentos
    err = nvs_set_blob(nvs_handle, "sched_l", pumplink_scheduling, sizeof(pumplink_scheduling));
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    err = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
    return err;
}

esp_err_t memory_load_scheduling()
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("sched", NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Carregar o horário da última atualização
    err = nvs_get_u32(nvs_handle, "sched_t", (uint32_t *)&last_sched_update);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Carregar o número de agendamentos
    err = nvs_get_u16(nvs_handle, "sched_c", &schedule_count);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Carregar a lista de agendamentos
    size_t size = sizeof(pumplink_scheduling);
    err = nvs_get_blob(nvs_handle, "sched_l", pumplink_scheduling, &size);
    nvs_close(nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Exibir a lista de agendamentos carregada
    ESP_LOGI(TAG_MEM, "Loaded Schedules List: %d items", schedule_count);
    for (int i = 0; i < schedule_count; i++) {
        ESP_LOGI(TAG_MEM, "Scheduling %d -> stime: %d, dweek: %d, nsteps: %d, wpump_idx: %d, wpump_wt: %d, allow_back: %d, back_idx: %d, allow_fert: %d, ferti_idx: %d, group: %d\n",
                i, pumplink_scheduling[i].start_time, pumplink_scheduling[i].days_of_week,
                pumplink_scheduling[i].number_of_steps, pumplink_scheduling[i].waterpump_dev_idx, 
                pumplink_scheduling[i].waterpump_working_time, pumplink_scheduling[i].allow_backwash,
                pumplink_scheduling[i].backwash_dev_idx, pumplink_scheduling[i].allow_ferti,
                pumplink_scheduling[i].ferti_dev_idx, pumplink_scheduling[i].group);
    }

    return ESP_OK;
}

esp_err_t memory_save_device_scheduling()
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("dev_sched", NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Horário da última atualização
    err = nvs_set_u32(nvs_handle, "dev_sched_t", last_dev_sched_update);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Salvar o número de agendamentos
    err = nvs_set_u16(nvs_handle, "dev_sched_c", schedules_per_device);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Salvar a lista de agendamentos
    err = nvs_set_blob(nvs_handle, "dev_sched_l", pumplink_device_scheduling, sizeof(pumplink_device_scheduling));
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    err = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
    return err;
}

esp_err_t memory_load_device_scheduling()
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("dev_sched", NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Carregar o horário da última atualização
    err = nvs_get_u32(nvs_handle, "dev_sched_t", (uint32_t *)&last_dev_sched_update);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Carregar o número de agendamentos
    err = nvs_get_u16(nvs_handle, "dev_sched_c", &schedules_per_device);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Carregar a lista de agendamentos
    size_t size = sizeof(pumplink_device_scheduling);
    err = nvs_get_blob(nvs_handle, "dev_sched_l", pumplink_device_scheduling, &size);
    nvs_close(nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Exibir a lista de agendamentos carregada
    ESP_LOGI(TAG_MEM, "Loaded Device Schedules List: %d items", schedules_per_device);
    for (int i = 0; i < schedules_per_device; i++) {
        ESP_LOGI(TAG_MEM, "Device Scheduling %d -> shc_idx: %d, dev_idx: %d, step: %d, sector_working_time: %d, ferti_working_time: %d, ferti_delay: %d",
            i, pumplink_device_scheduling[i].shc_idx, pumplink_device_scheduling[i].dev_idx,
                pumplink_device_scheduling[i].step, pumplink_device_scheduling[i].sector_working_time,
                pumplink_device_scheduling[i].ferti_working_time, pumplink_device_scheduling[i].ferti_delay);
    }

    return ESP_OK;
}

esp_err_t memory_save_automation()
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("auto", NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Salvar horário da última atualização
    err = nvs_set_u32(nvs_handle, "auto_t", last_auto_update);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Salvar o número de agendamentos
    err = nvs_set_u16(nvs_handle, "auto_c", automation_count);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Salvar a lista de agendamentos
    err = nvs_set_blob(nvs_handle, "auto_l", pumplink_automation, sizeof(pumplink_automation));
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    err = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
    return err;
}

esp_err_t memory_load_automation()
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("auto", NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Carregar o horário da última atualização
    err = nvs_get_u32(nvs_handle, "auto_t", (uint32_t *)&last_auto_update);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Carregar o número de agendamentos
    err = nvs_get_u16(nvs_handle, "auto_c", &automation_count);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Carregar a lista de agendamentos
    size_t size = sizeof(pumplink_automation);
    err = nvs_get_blob(nvs_handle, "auto_l", pumplink_automation, &size);
    nvs_close(nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Exibir a lista de agendamentos carregada
    ESP_LOGI(TAG_MEM, "Automation List: %d items", automation_count);
    for (int i = 0; i < automation_count; i++) {
        ESP_LOGI(TAG_MEM,"Automation %d -> level_idx: %d, pump_idx: %d, mask: %d, value: %d, enable: %d, working_time: %d\n",
                i, pumplink_automation[i].level_dev_idx, pumplink_automation[i].pump_dev_idx,
                pumplink_automation[i].mask, pumplink_automation[i].value, pumplink_automation[i].enabled,
                pumplink_automation[i].working_time);
    }

    return ESP_OK;
}

esp_err_t memory_save_config()
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("config", NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Horário da última atualização
    err = nvs_set_u32(nvs_handle, "time", last_config_update);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Configuração da retro lavagem
    err = nvs_set_blob(nvs_handle, "others", &pumplink_config, sizeof(pumplink_config));
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    err = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
    return err;
}

esp_err_t memory_load_config()
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("config", NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Carregar o horário da última atualização
    err = nvs_get_u32(nvs_handle, "time", (uint32_t *)&last_config_update);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Carregar a configuração da retro lavagem
    size_t size = sizeof(pumplink_config);
    err = nvs_get_blob(nvs_handle, "others", &pumplink_config, &size);
    nvs_close(nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    ESP_LOGI(TAG_MEM, "Loaded configurations:");
    ESP_LOGI(TAG_MEM, "  Backwash Cycle Time: %u", pumplink_config.backwash_cycle_time);
    ESP_LOGI(TAG_MEM, "  Backwash Duration: %u", pumplink_config.backwash_duration);
    ESP_LOGI(TAG_MEM, "  Backwash Delay (seconds): %u", pumplink_config.backwash_delay);
    ESP_LOGI(TAG_MEM, "  Raingauge Enabled: %u", pumplink_config.raingauge_enabled);
    ESP_LOGI(TAG_MEM, "  Raingauge Factor: %u", pumplink_config.raingauge_factor);
    ESP_LOGI(TAG_MEM, "  RainFall Limit: %u", pumplink_config.rainfall_limit);
    ESP_LOGI(TAG_MEM, "  RainFall Pause Duration: %u", pumplink_config.rainfall_pause_duration);

    return ESP_OK;
}
#ifndef MEMORY_H
#define MEMORY_H

#include "defines.h"
#include "mesh.h"

#include <stdint.h>
#include <string.h>
#include <ctype.h>
#include "esp_log.h"
#include "esp_wifi.h"
#include "nvs_flash.h"
#include "nvs.h"

esp_err_t memory_nvs_init();
void      memory_nvs_stats();
esp_err_t memory_save_system_info();
esp_err_t memory_load_system_info();
esp_err_t memory_save_wifi_credentials(char* ssid, char* password);
esp_err_t memory_load_wifi_credentials(char* ssid, size_t ssid_size, char* password, size_t password_size);
esp_err_t memory_save_devices();
esp_err_t memory_load_devices();
esp_err_t memory_save_scheduling();
esp_err_t memory_load_scheduling();
esp_err_t memory_save_device_scheduling();
esp_err_t memory_load_device_scheduling();
esp_err_t memory_save_automation();
esp_err_t memory_load_automation();
esp_err_t memory_save_config();
esp_err_t memory_load_config();

#endif // MEMORY_H
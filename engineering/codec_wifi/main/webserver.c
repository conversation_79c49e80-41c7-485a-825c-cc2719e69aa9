#include "webserver.h"
#include "outgoing_packet.pb-c.h"

static const char *TAG_WSERV = "WSERV";
extern SemaphoreHandle_t data_mutex;
extern TaskHandle_t      data_task_handle;
extern uint8_t mac[6];

httpd_handle_t webserver_init(void)
{
    httpd_config_t config = HTTPD_DEFAULT_CONFIG();
    config.max_resp_headers = 20;
    config.max_uri_handlers = 20;
    config.uri_match_fn = httpd_uri_match_wildcard;
    config.stack_size = 8192;

    httpd_handle_t server = NULL;

    if (httpd_start(&server, &config) == ESP_OK) {
        httpd_uri_t report = {
            .uri       = "/report",
            .method    = HTTP_POST,
            .handler   = report_post_handler,
            .user_ctx  = NULL
        };

        httpd_uri_t data = {
            .uri       = "/data",
            .method    = HTTP_POST,
            .handler   = data_post_handler,
            .user_ctx  = NULL
        };
        
        if (httpd_register_uri_handler(server, &report) != ESP_OK) {
            ESP_LOGE(TAG_WSERV, "Failed to register URI handler for /report");
            httpd_stop(server);
            return NULL;
        }

        if (httpd_register_uri_handler(server, &data) != ESP_OK) {
            ESP_LOGE(TAG_WSERV, "Failed to register URI handler for /data");
            httpd_stop(server);
            return NULL;
        }

        
    } else {
        ESP_LOGE(TAG_WSERV, "Failed to start web server");
    }

    return server;
}

esp_err_t check_basic_auth(httpd_req_t *req)
{
    char auth_header[128] = {0};
    size_t auth_header_len = httpd_req_get_hdr_value_len(req, "Authorization");

    if (auth_header_len == 0) {
        httpd_resp_set_status(req, "401 Unauthorized");
        httpd_resp_set_hdr(req, "WWW-Authenticate", "Basic realm=\"Codec\"");
        httpd_resp_send(req, NULL, 0);
        return ESP_FAIL;
    }

    if (auth_header_len >= sizeof(auth_header)) {
        ESP_LOGE(TAG_WSERV, "Auth header too long");
        return ESP_FAIL;
    }

    httpd_req_get_hdr_value_str(req, "Authorization", auth_header, sizeof(auth_header));

    // Verifica se o cabeçalho começa com "Basic "
    if (strncmp(auth_header, "Basic ", 6) != 0) {
        ESP_LOGE(TAG_WSERV, "Invalid auth header format");
        return ESP_FAIL;
    }

    char decoded[64];
    size_t decoded_len;
    char *encoded = auth_header + 6;

    if (mbedtls_base64_decode((unsigned char *)decoded, sizeof(decoded), &decoded_len,
                              (const unsigned char *)encoded, strlen(encoded)) != 0) {
        ESP_LOGE(TAG_WSERV, "Failed to decode base64");
        return ESP_FAIL;
    }

    decoded[decoded_len] = '\0';

    char expected[64];
    snprintf(expected, sizeof(expected), "%s:%s", BASIC_AUTH_USERNAME, BASIC_AUTH_PASSWORD);

    if (strcmp(decoded, expected) != 0) {
        ESP_LOGE(TAG_WSERV, "Invalid credentials: %s", decoded);
        httpd_resp_set_status(req, "401 Unauthorized");
        httpd_resp_set_hdr(req, "WWW-Authenticate", "Basic realm=\"Codec\"");
        httpd_resp_send(req, NULL, 0);
        return ESP_FAIL;
    }

    return ESP_OK;
}

esp_err_t report_post_handler(httpd_req_t *req)
{
    if (req->method != HTTP_POST) {
        httpd_resp_send_err(req, HTTPD_405_METHOD_NOT_ALLOWED,
                            "Use HTTP POST for /report");
        return ESP_FAIL;
    }

    if (check_basic_auth(req) != ESP_OK) {
        return ESP_FAIL;
    }

    ESP_LOGI(TAG_WSERV, "Request Report");

    if (req->content_len != 1) {
        httpd_resp_send_err(req, HTTPD_400_BAD_REQUEST, "Body must be exactly 1 byte (report type)");
        return ESP_FAIL;
    }

    char type = 0;
    int received = httpd_req_recv(req, (char *)&type, 1);
    if (received <= 0) {
        httpd_resp_send_err(req, HTTPD_500_INTERNAL_SERVER_ERROR, "Failed to read request body");
        return ESP_FAIL;
    }

    uint8_t *response = NULL;
    int len = 0;
    switch (type) {
        case CODEC__OUT__OUTGOING_PACKET__PAYLOAD_INFO:
            len = report_info(&response, S_REP_INFO_ALL);
            break;
        case CODEC__OUT__OUTGOING_PACKET__PAYLOAD_STATUS:
            len = report_status(&response);
            break;
        default:
            httpd_resp_send_err(req, HTTPD_400_BAD_REQUEST, "Unknown report type");
            return ESP_FAIL;
    }

    if(response == NULL){
        httpd_resp_send_err(req, 503, "Failed to get response");
        return ESP_FAIL;
    }
    
    httpd_resp_set_type(req, "application/x-protobuf");
    esp_err_t err = httpd_resp_send(req, (const char *)response, len);
    
    free(response);

    return ESP_OK;
}

esp_err_t data_post_handler(httpd_req_t *req)
{
    if (check_basic_auth(req) != ESP_OK) {
        return ESP_FAIL;
    }

    size_t total_len = req->content_len;

    if (req->content_len >= MAX_DATA_SIZE) {
        ESP_LOGE(TAG_WSERV, "Content too large");
        httpd_resp_send_err(req, HTTPD_400_BAD_REQUEST, "Content too large");
        return ESP_FAIL;
    }

    if (xSemaphoreTake(data_mutex, 0) != pdTRUE) {
        ESP_LOGW(TAG_WSERV, "Buffer busy – rejecting request");
        httpd_resp_send_err(req, 503, "Buffer busy, try again");
        return ESP_FAIL;
    }

    int r = httpd_req_recv(req, (char *)protocol.data, total_len);
    if (r != (int)total_len) {
        ESP_LOGE(TAG_WSERV, "Recv error (%d vs %d)", r, (int)total_len);
        xSemaphoreGive(data_mutex);
        httpd_resp_send_err(req, HTTPD_500_INTERNAL_SERVER_ERROR, "Recv failed");
        return ESP_FAIL;
    }

    ESP_LOGI(TAG_WSERV, "Data received length: %d", total_len);

    protocol.len = total_len;
    protocol.origin = P_WIFI;

    xSemaphoreGive(data_mutex);
    xTaskNotifyGive(data_task_handle);

    httpd_resp_set_status(req, HTTPD_204); // "204 No Content"
    esp_err_t err = httpd_resp_send(req, NULL, 0); // Content-Length: 0

    return err;
}
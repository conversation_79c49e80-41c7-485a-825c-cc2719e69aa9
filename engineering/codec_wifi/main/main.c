#include "main.h"
#include "memory.h"
#include "mqtt.h"
#include "webserver.h"
#include "protocol.h"
#include "mesh.h"
#include "report.h"
#include "esp_task_wdt.h"
#include "esp_timer.h"

static const char *TAG_M = "MAIN";

#define ANSI_COLOR_RED     "\x1b[31m"
#define ANSI_COLOR_GREEN   "\x1b[32m"
#define ANSI_COLOR_YELLOW  "\x1b[33m"
#define ANSI_COLOR_BLUE    "\x1b[34m"
#define ANSI_COLOR_MAGENTA "\x1b[35m"
#define ANSI_COLOR_CYAN    "\x1b[36m"
#define ANSI_COLOR_RESET   "\x1b[0m"
#define GPIO_INPUT_IO_34    34
#define ESP_INTR_FLAG_DEFAULT 0

#define MEMORY_DEBUG true

uint8_t mac[6];
static const int UART_BUF_SIZE = 1024;
char sta_ssid[MAX_STA_SSID_SIZE] = {0};
char sta_pass[MAX_STA_PASS_SIZE] = {0};
int rainfall_count[24] = {0};
int current_rainfall_hour = 0;
int rainfall_last24h = 0;
bool its_raining = 0;
bool new_scheduling_event_to_report = false;
time_t last_reported_scheduling = 0;
time_t last_reported_automation = 0;
time_t last_reported_status = 0;
time_t now, last_gpio_interruption = 0;
time_t last_its_raining = 0;

static QueueHandle_t uart_queue;
StaticSemaphore_t data_mutex_cb;
SemaphoreHandle_t data_mutex;
TaskHandle_t      data_task_handle;

static QueueHandle_t gpio_evt_queue = NULL;
extern esp_mqtt_client_handle_t mqtt_client;
static esp_timer_handle_t reconnect_timer;
int wifi_retry_count = 0;

static void IRAM_ATTR gpio_isr_handler(void* arg) {
    uint32_t gpio_num = (uint32_t) arg;
    int level = gpio_get_level(gpio_num);
    if (level == 0) {
        xQueueSendFromISR(gpio_evt_queue, &gpio_num, NULL);
    }
}

void reconnect_timer_callback(void* arg) {
    ESP_LOGI(TAG_M, "Timer expired, trying to reconnect...");
    esp_wifi_connect();
}

void start_reconnect_timer() {
    const esp_timer_create_args_t timer_args = {
        .callback = &reconnect_timer_callback,
        .name = "reconnect_timer"
    };
    esp_timer_create(&timer_args, &reconnect_timer);
    esp_timer_start_once(reconnect_timer, 60 * 1000000); // 60s
}

void wifi_event_handler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data) {
    if (event_base == WIFI_EVENT) {
        if (event_id == WIFI_EVENT_STA_START) {
            esp_wifi_connect();

        } else if (event_id == WIFI_EVENT_STA_DISCONNECTED) {
            if (mqtt_connected){
                mqtt_connected = false; 
                esp_mqtt_client_stop(mqtt_client);  // parar MQTT e tentar reconectar
            } 
            if (wifi_retry_count++ < MAX_RETRY) {
                esp_wifi_connect();
                ESP_LOGI(TAG_M, "Retry %d/%d", wifi_retry_count, MAX_RETRY);
            } else {
                ESP_LOGW(TAG_M, "Max retries reached. Waiting 60s.");
                wifi_retry_count = 0;
                start_reconnect_timer();
            }

        } else if (event_id == WIFI_EVENT_AP_START) {
            ESP_LOGI(TAG_M, "Soft-AP started");
        }
    } else if (event_base == IP_EVENT) {
        if (event_id == IP_EVENT_STA_GOT_IP) {
            ip_event_got_ip_t *evt = (ip_event_got_ip_t *)event_data;
            ESP_LOGI(TAG_M, "Got IP: " IPSTR, IP2STR(&evt->ip_info.ip));
            wifi_retry_count = 0;
            if (reconnect_timer) esp_timer_stop(reconnect_timer);
            if (!mqtt_connected) {
                esp_mqtt_client_start(mqtt_client);
            }
        } else if (event_id == IP_EVENT_STA_LOST_IP) {
            ESP_LOGW(TAG_M, "Lost IP lease");
            if (mqtt_connected){
                mqtt_connected = false; 
                esp_mqtt_client_stop(mqtt_client); // ainda ligado ao AP, mas sem DHCP, parar MQTT
            }
            esp_wifi_disconnect();
            esp_wifi_connect();
        }
    }
}

void wifi_init_softap_sta() {
    esp_netif_init();
    esp_event_loop_create_default();

    esp_netif_t *ap_netif = esp_netif_create_default_wifi_ap();
    
    esp_netif_ip_info_t ip_info;
    esp_netif_set_ip4_addr(&ip_info.ip, 192, 168, 4, 1);
    esp_netif_set_ip4_addr(&ip_info.gw, 192, 168, 4, 1);
    esp_netif_set_ip4_addr(&ip_info.netmask, 255, 255, 255, 0);
    esp_netif_dhcps_stop(ap_netif); // Stop DHCP server
    esp_netif_set_ip_info(ap_netif, &ip_info); // Assign static IP
    esp_netif_dhcps_start(ap_netif); // Restart DHCP server

    //esp_netif_create_default_wifi_ap();
    esp_netif_create_default_wifi_sta();
    
    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    esp_wifi_init(&cfg);
    
    esp_event_handler_instance_t instance_any_id;
    esp_event_handler_instance_t instance_got_ip;
    esp_event_handler_instance_register(WIFI_EVENT, ESP_EVENT_ANY_ID, &wifi_event_handler, NULL, &instance_any_id);
    esp_event_handler_instance_register(IP_EVENT, IP_EVENT_STA_GOT_IP, &wifi_event_handler, NULL, &instance_got_ip);
    
    wifi_config_t wifi_config_sta = {
        .sta = {
            .ssid = {0},
            .password = {0}
        },
    };
    
    strncpy((char*)wifi_config_sta.sta.ssid, sta_ssid, sizeof(wifi_config_sta.sta.ssid));
    strncpy((char*)wifi_config_sta.sta.password, sta_pass, sizeof(wifi_config_sta.sta.password));

    char wifi_ap_ssid[20];
    sprintf(wifi_ap_ssid, "Codec_%02X%02X%02X%02X%02X%02X", mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);

    wifi_config_t wifi_ap_config = {
        .ap = {
            .ssid_len = strlen(wifi_ap_ssid),
            .password = WIFI_AP_PASS,
            .max_connection = MAX_STA_CONN,
            .authmode = WIFI_AUTH_WPA2_PSK,
            .channel = 6 // Escolha um canal
        },
    };

    strncpy((char *)wifi_ap_config.ap.ssid, wifi_ap_ssid, sizeof(wifi_ap_config.ap.ssid));
    
    esp_wifi_set_mode(WIFI_MODE_APSTA);
    esp_wifi_set_config(WIFI_IF_STA, &wifi_config_sta);
    esp_wifi_set_config(WIFI_IF_AP, &wifi_ap_config);
    esp_wifi_start();
}

void uart_init(void)
{
    const uart_config_t uart_config = {
        .baud_rate = 115200,
        .data_bits = UART_DATA_8_BITS,
        .parity    = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE
    };
    ESP_ERROR_CHECK(uart_param_config(UART_NUM, &uart_config));
    ESP_ERROR_CHECK(uart_set_pin(UART_NUM, TXD_PIN, RXD_PIN, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE));
    ESP_ERROR_CHECK(uart_driver_install(UART_NUM, UART_BUF_SIZE, UART_BUF_SIZE, 10, &uart_queue, 0));

    uart_enable_pattern_det_baud_intr(UART_NUM, '}', 1, 9, 0, 0);
    uart_pattern_queue_reset(UART_NUM, 20);
}

static inline void initialize_sntp(void) 
{
    sntp_setoperatingmode(SNTP_OPMODE_POLL);
    sntp_setservername(0, "pool.ntp.org");
    sntp_init();

    setenv("TZ", "BRT3BRST,M10.3.0/0,M2.3.0/0", 1);
    tzset();
}

static inline void set_initial_rtc(void)
{
    struct tm start_tm = { 
        .tm_year = 2025 - 1900,
        .tm_mon  = 6,
        .tm_mday = 9,
        .tm_hour = 23,
        .tm_min  = 55,
        .tm_sec  = 0,
        .tm_isdst = -1
    };

    setenv("TZ", "BRT3BRST,M10.3.0/0,M2.3.0/0", 1);
    tzset(); // Aplica o fuso horário -3
    time_t epoch = mktime(&start_tm);

    struct timeval tv = { .tv_sec = epoch, .tv_usec = 0 };
    settimeofday(&tv, NULL);
}

struct s_pumplink_device_scheduling *get_device_scheduling(int shc_idx, int current_step) {
    for (int j = 0; j < MAX_MESH_DEVICE_SCHEDULING; j++) {
        if (pumplink_device_scheduling[j].shc_idx == shc_idx &&
            pumplink_device_scheduling[j].step == current_step) {
            return &pumplink_device_scheduling[j];
        }
    }
    return NULL; // Se não encontrar
}

static inline bool group_conflict(int sch_idx)
{
    if (pumplink_scheduling_state[sch_idx].working)
        return false;

    uint8_t g = pumplink_scheduling[sch_idx].group;

    for (int k = 0; k < schedule_count; k++) {
        if (k == sch_idx)   // ignora a própria entrada
            continue;

        if (pumplink_scheduling_state[k].working && pumplink_scheduling[k].group == g)
        {
            return true; // encontrou conflito
        }
    }
    return false; // nenhum conflito encontrado
}

static inline void mqtt_report()
{
    if(difftime(now, last_reported_scheduling) > 70 || new_scheduling_event_to_report){
        last_reported_scheduling = now;
        new_scheduling_event_to_report = false;
        uint8_t *buffer = NULL;
        int len = report_scheduling(&buffer);
        if (buffer != NULL && len > 0) {
            if (mqtt_client != NULL) {
                ESP_LOGI(TAG_M, "MQTT Report scheduling len: %d", len);
                int res = esp_mqtt_client_enqueue(mqtt_client, publish_topic_report, (const char*)buffer, len, 1, false, true);
                if (res < 0) {
                    ESP_LOGW(TAG_M, "MQTT enqueue failed: %d", res);
                } else {
                    ESP_LOGI(TAG_M, "MQTT enqueued msg_id=%d", res);
                }

            }
        }
        free(buffer);
    }if(difftime(now, last_reported_automation) > 80){
        last_reported_automation = now;
        uint8_t *buffer = NULL;
        int len = report_automation(&buffer);
        if (buffer != NULL && len > 0) {
            if (mqtt_client != NULL) {
                ESP_LOGI(TAG_M, "MQTT Report automation len: %d", len);
                int res = esp_mqtt_client_enqueue(mqtt_client, publish_topic_report, (const char*)buffer, len, 1, false, true);
                if (res < 0) {
                    ESP_LOGW(TAG_M, "MQTT enqueue failed: %d", res);
                } else {
                    ESP_LOGI(TAG_M, "MQTT enqueued msg_id=%d", res);
                }
            }
        }
        free(buffer);
    }else if(difftime(now, last_reported_status) > 60){
        last_reported_status = now;
        uint8_t *buffer = NULL;
        int len = report_status(&buffer);
        if (buffer != NULL && len > 0) {
            if (mqtt_client != NULL) {
                ESP_LOGI(TAG_M, "MQTT Report status len: %d", len);
                int res = esp_mqtt_client_enqueue(mqtt_client, publish_topic_report, (const char*)buffer, len, 1, false, true);
                if (res < 0) {
                    ESP_LOGW(TAG_M, "MQTT enqueue failed: %d", res);
                } else {
                    ESP_LOGI(TAG_M, "MQTT enqueued msg_id=%d", res);
                }
            }
        }
        free(buffer);
    }
}

static inline void update_rainfall_last24h()
{
    rainfall_last24h = 0;
    for(int i=0; i<24; i++){
        rainfall_last24h += rainfall_count[i];
    }
    rainfall_last24h /= pumplink_config.raingauge_factor;
}

static inline bool is_valid_dev_idx(unsigned idx) {
    return idx >= 0 && idx < MAX_MESH_DEVICES;
}

static void reset_scheduling_flags(int shc_idx)
{
    pumplink_scheduling_state[shc_idx].current_step            = 0;
    pumplink_scheduling_state[shc_idx].total_working_time_secs = 0;
    pumplink_scheduling_state[shc_idx].init_info               = false;
    pumplink_scheduling_state[shc_idx].started                 = false;
    pumplink_scheduling_state[shc_idx].working                 = false;
    //pumplink_scheduling_state[shc_idx].last_dev_idx            = -1;

    for (int j = 0; j < MAX_MESH_DEVICE_SCHEDULING; ++j) {
        if (pumplink_device_scheduling[j].shc_idx != shc_idx)
            continue;

        int dev_idx = pumplink_device_scheduling[j].dev_idx;
        if (!is_valid_dev_idx(dev_idx))
            continue;

        pumplink_device_state[dev_idx].triggered           = false;
        pumplink_device_state[dev_idx].working_time_secs   = 0;
        pumplink_device_scheduling_state[dev_idx].tried    = false;
        pumplink_device_scheduling_state[dev_idx].executed = false;
        pumplink_device_scheduling_state[dev_idx].canceled = false;
        pumplink_device_scheduling_state[dev_idx].info     = false;
    }

    if(pumplink_scheduling[shc_idx].waterpump_working_time > 0){
        int pump_idx = pumplink_scheduling[shc_idx].waterpump_dev_idx;
        if (is_valid_dev_idx(pump_idx))
            pumplink_device_scheduling_state[pump_idx].canceled = false;
    }

    if(pumplink_scheduling[shc_idx].allow_ferti){
        int ferti_idx = pumplink_scheduling[shc_idx].ferti_dev_idx;
        if (is_valid_dev_idx(ferti_idx))
            pumplink_device_state[ferti_idx].triggered = false;
    }
}

void reset_all_scheduling_flags(void){
    memset(pumplink_scheduling_state,        0, sizeof(pumplink_scheduling_state));
    memset(pumplink_device_scheduling_state, 0, sizeof(pumplink_device_scheduling_state));

    for (int i = 0; i < device_count && i < MAX_MESH_DEVICES; ++i) {
        pumplink_device_state[i].triggered         = false;
        pumplink_device_state[i].working_time_secs = 0;
        pumplink_device_state[i].last_appoint      = 0;
    }
}

static void handle_uart_exception(void)
{
    uart_flush_input(UART_NUM);             // descarta FIFO + ring-buffer
    uart_pattern_queue_reset(UART_NUM, 20);
    xQueueReset(uart_queue);
    ESP_LOGW(TAG_M, "UART Exception: Flush and Clear");
}

static inline time_t get_today_midnight(time_t now)
{
    struct tm tm_midnight;
    localtime_r(&now, &tm_midnight);
    tm_midnight.tm_hour = 0;
    tm_midnight.tm_min  = 0;
    tm_midnight.tm_sec  = 0;
    return mktime(&tm_midnight);
}

bool is_scheduling_running(void)
{
    for (int i = 0; i < MAX_MESH_SCHEDULING; ++i) {
        if (pumplink_scheduling_state[i].started ||
            pumplink_scheduling_state[i].working)
            return true; // há agendamento ativo
    }
    return false;
}

void main_task(void *pvParameters) {  
    uart_event_t event;
    time(&now);

    time_t last_scheduling_time = 0, last_info_sch_time = 0;
    time_t wdt_time = 0, memory_debug_time = 0, fail_warning_time = 0;
    double task_max_duration = 0;
    double task_duration = 0;
    int io_num;
    char mac_strig[64];
    char uart_data[UART_BUF_SIZE];

    last_reported_scheduling = now;
    last_reported_status = now;
    pause_scheduling = false;
    pause_scheduling_time = 0;
    pause_scheduling_timer = 12*3600; // 12 horas

    sprintf(mac_strig, "%02X%02X%02X%02X%02X%02X",mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
    ESP_LOGI(TAG_M, ANSI_COLOR_CYAN "Codec ID: %s" ANSI_COLOR_CYAN, mac_strig);
    ESP_LOGI(TAG_M, ANSI_COLOR_CYAN "ESP FW: %d, MESH FW: %d, HARD VER: %d" ANSI_COLOR_CYAN, ESP_FW_VERSION, MESH_FW_VERSION, HARDWARE_VERSION);

    vTaskDelay(pdMS_TO_TICKS(100)); // Aguarda 100ms para garantir que o sistema esteja pronto
    memory_save_system_info(); // Atualiza o número de reinicializações
    report_init();
    esp_task_wdt_add(NULL); // Adiciona a tarefa atual ao watchdog

    while (1) {
        int64_t task_start_loop = esp_timer_get_time();
        time(&now);

        struct tm timeinfo;
        localtime_r(&now, &timeinfo);
        int weekday = timeinfo.tm_wday;
        time_t today_midnight = get_today_midnight(now);
        time_t current_ts = now;

        // Verifica a cada 1 segundo
        if (difftime(now, last_scheduling_time) >= 1) {
            last_scheduling_time = now;

            // Percorre todos os agendamentos
            for (int i = 0; i < schedule_count; i++) {

                // Se já ouver um agendamento em andamento no mesmo grupo, aguarda para executar o novo agendamento
                if (group_conflict(i)){
                    if (difftime(now, pumplink_scheduling_state[i].last_log_info) >= TIMER_20S) {
                        pumplink_scheduling_state[i].last_log_info = now;
                        ESP_LOGI(TAG_M, ANSI_COLOR_CYAN "[SCH %d]: Group Conflict" ANSI_COLOR_RESET, i);
                    }
                    continue;
                }

                if(!(pumplink_scheduling[i].days_of_week & (1 << weekday)) && !pumplink_scheduling_state[i].working){ // Se o dia da semana não está ativo e se o agendamento não foi iniciado, pula para o próximo agendamento
                    continue;
                }

                if (pumplink_scheduling_state[i].working == false) {
                    pumplink_scheduling_state[i].reference_day = today_midnight;
                }else{
                    if(pumplink_scheduling[i].waterpump_working_time > 0){
                        int elapsed_time = difftime(now, pumplink_scheduling_state[i].reference_day + pumplink_scheduling[i].start_time);
                        if(elapsed_time > pumplink_scheduling[i].waterpump_working_time*60 + TIMER_4H) {
                            reset_scheduling_flags(i);
                            ESP_LOGE(TAG_M, "[SCH %d]: Worktime exceeded 4h — elapsed: %d", i, elapsed_time);
                        }
                    }
                }
                
                int step = pumplink_scheduling_state[i].current_step;
                struct s_pumplink_device_scheduling *current_dev_scheduling = get_device_scheduling(i, step);
                if (!current_dev_scheduling) {
                    if (difftime(now, pumplink_scheduling_state[i].fail_devsch_log) >= TIMER_20S) {
                        pumplink_scheduling_state[i].fail_devsch_log = now;
                        ESP_LOGE(TAG_M, "[SCH %d]: Failed to get device schedule", i);
                    }
                    continue;
                }

                time_t reference_day_ts  = pumplink_scheduling_state[i].reference_day;
                time_t schedule_ts = reference_day_ts + pumplink_scheduling[i].start_time;
                time_t sector_schedule_ts = schedule_ts + pumplink_scheduling_state[i].total_working_time_secs;
                time_t ferti_schedule_ts = sector_schedule_ts + current_dev_scheduling->ferti_delay*60;
                int sector_diff = difftime(current_ts, sector_schedule_ts);
                int ferti_diff = difftime(current_ts, ferti_schedule_ts);
                int sector_working_time = current_dev_scheduling->sector_working_time;
                int ferti_working_time = current_dev_scheduling->ferti_working_time;
                int max_sector_diff = sector_working_time*60 > TIMER_3MIN ? TIMER_3MIN : sector_working_time*60;
                int max_pump_diff = pumplink_scheduling[i].waterpump_working_time*60 > TIMER_10MIN ? TIMER_10MIN : pumplink_scheduling[i].waterpump_working_time*60;
                int max_ferti_diff = max_sector_diff;
                int max_backwash_diff = max_sector_diff;
                int k = current_dev_scheduling->dev_idx;
                int p = pumplink_scheduling[i].waterpump_dev_idx;

                if (!is_valid_dev_idx(k)) { // Verifica se o índice do dispositivo é inválido
                    if (difftime(now, fail_warning_time) >= TIMER_20S) {
                        fail_warning_time = now;
                        ESP_LOGW(TAG_M, "[SCH %d]: Invalid index: dev_idx=%d", i, k);
                    }
                    continue;
                }

                if(pumplink_scheduling_state[i].init_info == false) {
                    ESP_LOGI(TAG_M, ANSI_COLOR_BLUE "Weekday: %d - %d" ANSI_COLOR_RESET, pumplink_scheduling[i].days_of_week, (1 << weekday));
                    ESP_LOGI(TAG_M, ANSI_COLOR_BLUE "[SCH %d]: curr_secs: %d, schd_secs: %d, schd_fert: %d" ANSI_COLOR_RESET, i, (int)current_ts, (int)sector_schedule_ts, (int)ferti_schedule_ts);
                    pumplink_scheduling_state[i].init_info = true;
                }

                if (difftime(now, pumplink_scheduling_state[i].last_log_info) >= TIMER_20S) {
                    pumplink_scheduling_state[i].last_log_info = now;
                    int total_elapsed_time = difftime(now, schedule_ts)/60;
                    ESP_LOGI(TAG_M, ANSI_COLOR_CYAN "[SCH %d]: step: %d, elap_time: %d, ref_day: %d, schd_secs: %d, curr_secs: %d, curr_wt: %d, t_wt: %d, s_diff: %d, f_diff: %d, pause %d" ANSI_COLOR_RESET, 
                            i, step, total_elapsed_time, (int)reference_day_ts, (int)sector_schedule_ts, (int)current_ts, pumplink_device_state[k].working_time_secs, pumplink_scheduling_state[i].total_working_time_secs, sector_diff, ferti_diff, pause_scheduling);
                }

                // Somente se diff estiver entre 0 e max_diff
                if (sector_diff > 0 && sector_diff <= max_sector_diff && !pause_scheduling) {
                    if (pumplink_device_state[k].triggered == false) { // Se o dispositivo não foi ativado
                        // Se o dispositivo estiver desligado, envia o comando
                        if (difftime(now, last_info_sch_time) >= TIMER_30S) {
                            last_info_sch_time = now;
                            ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[SCH %d]: dev_sched: %d, current_sec: %d, schedule_sec: %d" ANSI_COLOR_RESET, i, k, (int)current_ts, (int)sector_schedule_ts);
                        }
                        if (pumplink_device_state[k].device_status == 0) {
                            if ((now - pumplink_device_state[k].last_cmd) > TIMER_30S) { // 30 segundos
                                char mesh_pkg[64];
                                int additional_time = 2;
                                if(step == pumplink_scheduling[i].number_of_steps-1){
                                    additional_time = 3; // Se for o último passo, adiciona 3 minutos
                                }
                                int sector_working_time_fix = sector_working_time+additional_time; // Soma o tempo adicional
                                mesh_pumplink_turn_on_encode(pumplink_devices[k].meshid, pumplink_devices[k].device_id, pumplink_devices[k].out1, pumplink_devices[k].out2, pumplink_devices[k].input, pumplink_devices[k].mode, sector_working_time_fix, mesh_pkg);
                                uart_tx_chars(UART_NUM, mesh_pkg, strlen(mesh_pkg));
                                
                                ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[SCH %d] Sending sector %d command to dev_idx=%d meshid=%d device_id=%d (working_time=%d+%d) step=%d" ANSI_COLOR_RESET,
                                        i, pumplink_devices[k].sector, k,
                                        pumplink_devices[k].meshid,
                                        pumplink_devices[k].device_id,
                                        sector_working_time, additional_time, step);

                                pumplink_device_state[k].last_cmd = now;
                                pumplink_device_state[k].working_time_secs = sector_working_time*60;
                                pumplink_scheduling_state[i].started = true;      // Incio do agendamento, limpa no final da programação
                                pumplink_device_scheduling_state[k].tried = true; // Limpa no final de trabalho de cada setor
                                pumplink_device_scheduling_state[k].executed = false;
                                pumplink_device_scheduling_state[k].canceled = false;
                                if(pumplink_scheduling[i].allow_ferti){ // garantir que o flag de fertilização esteja limpa
                                    int f = pumplink_scheduling[i].ferti_dev_idx;
                                    pumplink_device_state[f].triggered = false;
                                }
                            }
                        } else { // Se o dispositivo estiver ligado
                            if(pumplink_device_state[k].triggered == false) {
                                pumplink_device_state[k].triggered = true; // Marcamos que está ativo
                                pumplink_device_state[k].triggered_time = now;
                                if(step == 0){
                                    report_scheduling_start(i, now);
                                }
                                pumplink_scheduling_report[i].sector_l1 |= (1 << step);
                                new_scheduling_event_to_report = true;

                                if(pumplink_scheduling_state[i].working){ // Se o agendamento já estiver em andamento, desliga a sobreposição
                                    unsigned int last_dev_idx = pumplink_scheduling_state[i].last_dev_idx;
                                    if (is_valid_dev_idx(last_dev_idx)) { // Verifica se o índice do último dispositivo é válido
                                        if(pumplink_device_state[last_dev_idx].device_status != 0 && pumplink_device_scheduling_state[last_dev_idx].canceled == false){ // Desliga a sobreposição se o último dispositivo estiver ligado e o atual também estiver ligado
                                            struct s_pumplink_control new_control;
                                            new_control.dev_idx = last_dev_idx;
                                            new_control.action = 2;
                                            new_control.working_time = 0;
                                            new_control.enabled = true;
                                            new_control.start_time = now;
                                            new_control.timeout = TIMER_2MIN;
                                            pumplink_device_scheduling_state[last_dev_idx].canceled = true; // Perminte apenas um único cancelamento por agendamento
                                            mesh_insert_pumplink_control(new_control);
                                            ESP_LOGW(TAG_M, "[SCH %d] Disable Overlay Command dev_idx=%d", i, last_dev_idx);
                                        }
                                    }
                                }
                                pumplink_scheduling_state[i].working = true; // Mantem até o final do agendamento
                                pumplink_device_scheduling_state[k].executed = true; // Limpa no final de trabalho de cada setor, mas garante que o setor foi executado
                                ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[SCH %d] Device dev_idx=%d is already on" ANSI_COLOR_RESET, i, k);
                            }
                        }
                    }
                } else {
                    if(pumplink_scheduling_state[i].started){
                        int sector_elapsed_time = difftime(now, pumplink_device_state[k].last_cmd); // Tempo decorrido desde a última ativação
                        
                        if(step >= pumplink_scheduling[i].number_of_steps-1 || pause_scheduling){ // Se for o último passo 
                            if((pumplink_device_scheduling_state[k].executed == false || sector_elapsed_time >= pumplink_device_state[k].working_time_secs) || pause_scheduling){
                                if(pumplink_device_scheduling_state[k].tried == true){
                                    ESP_LOGW(TAG_M, "[SCH %d] Programming completed", i);      
                                    pumplink_scheduling_state[i].current_step = 0;
                                    pumplink_scheduling_state[i].total_working_time_secs = 0;
                                    pumplink_scheduling_state[i].init_info = false;
                                    pumplink_scheduling_state[i].started = false;
                                    pumplink_scheduling_state[i].last_dev_idx = k;    // Guarda o último dispositivo acionado                                       
                                    pumplink_device_scheduling_state[k].tried = false;
                                    pumplink_device_scheduling_state[k].executed = false;
                                    pumplink_device_scheduling_state[k].info = false;
                                }

                                if(pumplink_scheduling[i].waterpump_working_time == 0 && pumplink_scheduling_state[i].working){ // Se não houver bomba de água, desliga o agendamento
                                    ESP_LOGW(TAG_M, "[SCH %d] Scheduling finished", i);
                                    pumplink_scheduling_state[i].working = false;
                                    pumplink_scheduling_report[i].status = S_REP_SCHED_END;
                                    reset_scheduling_flags(i); // Reseta os flags do agendamento
                                }
                            }
                        } else {
                            if (pumplink_device_scheduling_state[k].tried == true && (pumplink_device_scheduling_state[k].executed == false || sector_elapsed_time >= pumplink_device_state[k].working_time_secs)) { // Se o dispositivo estiver desligado ou se já passou o tempo de trabalho
                                                    
                                pumplink_scheduling_state[i].current_step++;
                                pumplink_scheduling_state[i].last_dev_idx = k;
                                pumplink_scheduling_state[i].total_working_time_secs = now - schedule_ts;
                                ESP_LOGW(TAG_M, "[SCH %d] Moving to next step: %d, total_working_time: %d", i, pumplink_scheduling_state[i].current_step, pumplink_scheduling_state[i].total_working_time_secs);
                                
                                pumplink_device_scheduling_state[k].tried = false;
                                pumplink_device_scheduling_state[k].executed = false;
                            }
                        }

                        if (pumplink_device_state[k].triggered == true) {
                            pumplink_device_state[k].triggered = false; // para enviar o comando apenas detro da janela de execução
                            ESP_LOGW(TAG_M, "[SCH %d] Sector %d dev_idx=%d triggered is false", i, pumplink_devices[k].sector, k);
                        }

                    }

                }

                if(pumplink_scheduling_state[i].working){
                    // Bomba de água
                    if(max_pump_diff > 0){
                        if(difftime(now, schedule_ts) < max_pump_diff && !pause_scheduling){ // Se estiver dentro de 10 minutos do horário de início
                            if (!pumplink_device_state[p].triggered && pumplink_device_state[k].device_status != 0) {
                                if (pumplink_device_state[p].device_status == 0) {
                                    if (difftime(now, pumplink_device_state[p].last_cmd) >= TIMER_30S) {
                                        char mesh_pkg[64];
                                        mesh_pumplink_turn_on_encode(pumplink_devices[p].meshid, pumplink_devices[p].device_id, pumplink_devices[p].out1, pumplink_devices[p].out2, pumplink_devices[p].input, pumplink_devices[p].mode, pumplink_scheduling[i].waterpump_working_time, mesh_pkg);
                                        uart_tx_chars(UART_NUM, mesh_pkg, strlen(mesh_pkg));
                                        
                                        ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[SCH %d] Sending waterpump command to dev_idx=%d meshid=%d device_id=%d (working_time=%d) step=%d" ANSI_COLOR_RESET,
                                            i, p,
                                            pumplink_devices[p].meshid,
                                            pumplink_devices[p].device_id,
                                            pumplink_scheduling[i].waterpump_working_time, step);

                                        pumplink_device_state[p].last_cmd = now;
                                        pumplink_device_state[p].working_time_secs = pumplink_scheduling[i].waterpump_working_time*60;
                                        pumplink_device_scheduling_state[p].canceled = false; // Será cancelado se o agendamento for pausado
                                        if(pumplink_scheduling[i].allow_backwash){
                                            int b = pumplink_scheduling[i].backwash_dev_idx;
                                            pumplink_device_backwash_state[b].attempt = false;
                                            pumplink_device_backwash_state[b].next_attempt_time = 0;
                                        }
                                    }
                                } else { // Se o dispositivo estiver ligado
                                    if(pumplink_device_state[p].triggered == false){
                                        pumplink_device_state[p].triggered = true; 
                                        pumplink_device_state[p].triggered_time = now;
                                        pumplink_scheduling_report[i].waterpump = true;
                                        new_scheduling_event_to_report = true;
                                        ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[SCH %d] WaterPump dev_idx=%d is already on" ANSI_COLOR_RESET, i, p);
                                    }
                                }
                            }
                        }else{
                            bool last_dev_turned_off = false;
                            if(!pumplink_scheduling_state[i].started){ // Se started é falso é porque já tentou acionar o ultimo setor
                                unsigned int last_dev_idx = pumplink_scheduling_state[i].last_dev_idx;
                                if (is_valid_dev_idx(last_dev_idx)) { // Verifica se o índice do último dispositivo é válido
                                    last_dev_turned_off = (pumplink_scheduling_state[i].working && pumplink_device_state[last_dev_idx].device_status == 0);
                                }
                            }
                            if(pumplink_device_state[p].device_status != 0 && pumplink_device_scheduling_state[p].canceled == false && (last_dev_turned_off || pause_scheduling)){ // Desligar a bomba de água se o ultimo setor já estiver desligado ou se o agendamento estiver pausado
                                struct s_pumplink_control new_control;
                                new_control.dev_idx = p;
                                new_control.action = 2;
                                new_control.working_time = 0;
                                new_control.enabled = true;
                                new_control.start_time = now;
                                new_control.timeout = TIMER_5MIN;
                                pumplink_device_scheduling_state[p].canceled = true; // Perminte apenas um único cancelamento por agendamento
                                mesh_insert_pumplink_control(new_control);
                                if(last_dev_turned_off) 
                                    ESP_LOGW(TAG_M, "[SCH %d] Last Sector is Turned Off dev_idx=%d", i, pumplink_scheduling_state[i].last_dev_idx);
                                ESP_LOGW(TAG_M, "[SCH %d] WaterPump Turn Off Command dev_idx=%d", i, p);
                            }
                            
                            if (pumplink_device_state[p].triggered == true) {
                                pumplink_device_state[p].triggered = false; 
                                ESP_LOGW(TAG_M, "[SCH %d] WaterPump dev_idx=%d triggered is false", i, p);
                            }

                            if(pumplink_scheduling_state[i].working && ((pumplink_device_state[p].device_status == 0) || (difftime(now, pumplink_device_state[p].last_cmd) > pumplink_device_state[p].working_time_secs+60))){
                                ESP_LOGW(TAG_M, "[SCH %d] Scheduling finished", i);
                                pumplink_scheduling_state[i].working = false;
                                pumplink_scheduling_report[i].status = S_REP_SCHED_END;
                                reset_scheduling_flags(i); // Reseta os flags do agendamento
                            }
                        }
                    }
                    
                    // Fertilização
                    if(pumplink_scheduling[i].allow_ferti){ // Se estiver habilitado e a bomba de água estiver ligada
                        int f = pumplink_scheduling[i].ferti_dev_idx;
                        int elapsed_time_sec_trigger = difftime(now, pumplink_device_state[k].last_cmd);
                        if(ferti_diff > 0 && ferti_diff <= max_ferti_diff && !pause_scheduling){
                            if((sector_working_time*60 - elapsed_time_sec_trigger) > (ferti_working_time*60+30)){ // Se o tempo restante de trabalho do setor for maior que o tempo de trabalho do fertilizante mais 30 segundos
                                if (!pumplink_device_state[f].triggered && pumplink_device_state[k].device_status != 0 && pumplink_device_state[p].device_status != 0) {
                                    if (pumplink_device_state[f].device_status == 0) {
                                        if (difftime(now, pumplink_device_state[f].last_cmd) >= TIMER_30S) {
                                            char mesh_pkg[64];
                                            //int ferti_working_time_fix = ferti_working_time-ferti_diff/60;
                                            mesh_pumplink_turn_on_encode(pumplink_devices[f].meshid, pumplink_devices[f].device_id, pumplink_devices[f].out1, pumplink_devices[f].out2, pumplink_devices[f].input, pumplink_devices[f].mode, ferti_working_time, mesh_pkg);
                                            uart_tx_chars(UART_NUM, mesh_pkg, strlen(mesh_pkg));
                                            
                                            ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[SCH %d] Sending Ferti command to dev_idx=%d meshid=%d device_id=%d (working_time=%d) step=%d" ANSI_COLOR_RESET,
                                                i, f,
                                                pumplink_devices[f].meshid,
                                                pumplink_devices[f].device_id,
                                                ferti_working_time, step);

                                            pumplink_device_state[f].last_cmd = now;
                                            pumplink_device_state[f].working_time_secs = ferti_working_time*60;
                                        }
                                    } else { // Se o dispositivo estiver ligado
                                        if(pumplink_device_state[f].triggered == false){
                                            pumplink_device_state[f].triggered = true;
                                            pumplink_device_state[f].triggered_time = now;
                                            pumplink_scheduling_report[i].ferti_l1 |= (1 << step);
                                            new_scheduling_event_to_report = true;
                                            ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[SCH %d] Ferti dev_idx=%d is already on" ANSI_COLOR_RESET, i, f);
                                        }
                                    }
                                }
                            }
                        }else{
                            if (pumplink_device_state[f].triggered == true) {
                                pumplink_device_state[f].triggered = false; 
                                ESP_LOGW(TAG_M, "[SCH %d] Ferti dev_idx=%d triggered is false", i, f);
                            }
                        }
                    }

                    // Retro-Lavagem
                    if(pumplink_scheduling[i].allow_backwash){
                        int b = pumplink_scheduling[i].backwash_dev_idx;

                        if(pumplink_scheduling[i].waterpump_working_time > 0 && pumplink_device_state[p].device_status != 0){

                            int backwash_inc_time = difftime(now, pumplink_device_backwash_state[b].inc_time);
                            if(backwash_inc_time >= 1){
                                pumplink_device_backwash_state[b].inc_time = now;
                                if(backwash_inc_time < TIMER_30S){ // Não pode ser maior que 30 segundos, pois o WDT deve disparar, e evita inc_time = 0
                                    pumplink_device_backwash_state[b].timer += backwash_inc_time;  // Incrementa o timer sem retro-lavagem
                                }
                            }

                            int pump_elapsed_time = difftime(now, pumplink_device_state[p].triggered_time);
                            int next_attempt_elap_time = difftime(now, pumplink_device_backwash_state[b].next_attempt_time);
                            if(pumplink_device_backwash_state[b].attempt == false && pumplink_device_backwash_state[b].timer > pumplink_config.backwash_cycle_time*60 && pump_elapsed_time > pumplink_config.backwash_delay && next_attempt_elap_time > 0){
                                pumplink_device_backwash_state[b].attempt = true;
                                pumplink_device_backwash_state[b].attempt_time = now;
                            }

                            if (difftime(now, pumplink_device_backwash_state[b].last_log_info) >= TIMER_20S) {
                                pumplink_device_backwash_state[b].last_log_info = now;
                                ESP_LOGI(TAG_M, ANSI_COLOR_CYAN "[SCH %d]: Backwash dev_idx: %d, timer: %d, attempt: %d, next_attempt_elap_time: %d" ANSI_COLOR_RESET, i, b, pumplink_device_backwash_state[b].timer, pumplink_device_backwash_state[b].attempt, next_attempt_elap_time);
                            }

                            if(pumplink_device_backwash_state[b].attempt){
                                int backwash_diff = difftime(now, pumplink_device_backwash_state[b].attempt_time);

                                if(backwash_diff > 0 && backwash_diff <= max_backwash_diff && !pause_scheduling){
                                    if((pumplink_scheduling[i].waterpump_working_time*60 - pump_elapsed_time) > (pumplink_config.backwash_duration*60+30)){ // Se o tempo restante de trabalho do setor for maior que o tempo de trabalho do fertilizante mais 30 segundos
                                        if (pumplink_device_state[b].device_status == 0) {
                                            if (difftime(now, pumplink_device_state[b].last_cmd) >= TIMER_30S) {
                                                char mesh_pkg[64];
                                                mesh_pumplink_turn_on_encode(pumplink_devices[b].meshid, pumplink_devices[b].device_id, pumplink_devices[b].out1, pumplink_devices[b].out2, pumplink_devices[b].input, pumplink_devices[b].mode, pumplink_config.backwash_duration, mesh_pkg);
                                                uart_tx_chars(UART_NUM, mesh_pkg, strlen(mesh_pkg));
                                                
                                                ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[SCH %d] Sending Backwash command to dev_idx=%d meshid=%d device_id=%d (working_time=%d) step=%d" ANSI_COLOR_RESET,
                                                    i, b,
                                                    pumplink_devices[b].meshid,
                                                    pumplink_devices[b].device_id,
                                                    pumplink_config.backwash_duration, step);

                                                pumplink_device_state[b].last_cmd = now;
                                                pumplink_device_state[b].working_time_secs = pumplink_config.backwash_duration*60;
                                            }
                                        } else { // Se o dispositivo estiver ligado
                                            pumplink_device_backwash_state[b].timer = 0; // Reseta o timer de retro-lavagem
                                            pumplink_device_backwash_state[b].attempt = false;
                                            pumplink_device_backwash_state[b].next_attempt_time = 0;
                                            pumplink_scheduling_report[i].backwash = true;
                                            new_scheduling_event_to_report = true;
                                            ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[SCH %d] Backwash dev_idx=%d is already on" ANSI_COLOR_RESET, i, b);
                                        }
                                    }
                                }else{
                                    if(backwash_diff > 0){
                                        pumplink_device_backwash_state[b].next_attempt_time = now + TIMER_10MIN;
                                        pumplink_device_backwash_state[b].attempt = false;
                                        ESP_LOGW(TAG_M, "[SCH %d] Backwash dev_idx=%d delay", i, b);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // Automatização de bomba usando controle de nível
            for (int i = 0; i < automation_count; i++) {
                int l = pumplink_automation[i].level_dev_idx; // Dispositivo medidor de nível
                int p = pumplink_automation[i].pump_dev_idx;  // Bomba de nivel

                int automation_diff = (int)difftime(now, pumplink_automation_state[i].next_time);
                if (difftime(now, pumplink_automation_state[i].last_log_info) >= TIMER_25S) {
                    pumplink_automation_state[i].last_log_info = now;
                    ESP_LOGI(TAG_M, ANSI_COLOR_CYAN "[AUTO %d]: level_idx: %d, pump_idx: %d, auto_diff: %d" ANSI_COLOR_RESET, i, l, p, automation_diff);
                }

                if(pumplink_automation[i].enabled && (pumplink_device_state[l].inputs & pumplink_automation[i].mask) == pumplink_automation[i].value && 
                difftime(now, pumplink_device_state[l].last_sync) < TIMER_20MIN && difftime(now, pumplink_device_state[p].last_sync) < TIMER_20MIN && pumplink_device_state[p].device_status == 0) { // Se a entrada estiver desligada e com dado atual, e a bomba estiver desligada
                    if(automation_diff > 0 && automation_diff <= TIMER_5MIN){
                        pumplink_automation_state[i].tried = true;
                        if (difftime(now, pumplink_device_state[p].last_cmd) >= TIMER_30S) {
                            char mesh_pkg[64];
                            mesh_pumplink_turn_on_encode(pumplink_devices[p].meshid, pumplink_devices[p].device_id, pumplink_devices[p].out1, pumplink_devices[p].out2, pumplink_devices[p].input, pumplink_devices[p].mode, pumplink_automation[i].working_time, mesh_pkg);
                            uart_tx_chars(UART_NUM, mesh_pkg, strlen(mesh_pkg));
                            
                            ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[AUTO %d] Sending Automation command to dev_idx=%d meshid=%d device_id=%d (working_time=%d)" ANSI_COLOR_RESET,
                                i, p,
                                pumplink_devices[p].meshid,
                                pumplink_devices[p].device_id,
                                pumplink_automation[i].working_time);

                            pumplink_device_state[p].last_cmd = now;
                            pumplink_device_state[p].working_time_secs = pumplink_automation[i].working_time*60;
                            pumplink_automation_state[i].canceled = false; // Será cancelado se o controle de nível indicar que a bomba deve desligar
                        }
                    }else{
                        if(automation_diff > 0){
                            ESP_LOGI(TAG_M, ANSI_COLOR_CYAN "[AUTO %d]: Before change next_time auto_diff: %d" ANSI_COLOR_RESET, i, automation_diff);
                            if(pumplink_automation_state[i].tried == false){ // Inicialização do next_time
                                pumplink_automation_state[i].next_time = now; 
                                ESP_LOGW(TAG_M, "[AUTO %d] Automation Next Time Reset", i);
                            }else { // Se passou pelo tempo de espera e ainda não conseguiu ligar a bomba
                                pumplink_automation_state[i].next_time = now + 600; // Agora+10min
                                pumplink_automation_state[i].tried = false;
                                ESP_LOGW(TAG_M, "[AUTO %d] Automation Next Time + Delay", i);
                            }
                        }
                    }
                }else{
                    if (pumplink_automation[i].enabled) { // Se a entrada estiver ligada ou a bomba estiver ligada
                        pumplink_automation_state[i].next_time = now;
                        if(pumplink_automation_state[i].tried == true){
                            pumplink_automation_state[i].tried = false;
                            if(pumplink_device_state[p].device_status != 0){
                                pumplink_automation_state[i].working = true; // Mantem até o final da automação
                                report_automation_start(i, now);
                                ESP_LOGW(TAG_M, "[AUTO %d] Automation LevelPump dev_idx=%d Working", i, p);
                            }
                        }

                        if(!pumplink_automation_state[i].canceled && difftime(now, pumplink_device_state[l].last_sync) < TIMER_20MIN && difftime(now, pumplink_device_state[p].last_sync) < TIMER_20MIN && 
                        (pumplink_device_state[l].inputs & pumplink_automation[i].mask) != pumplink_automation[i].value && pumplink_device_state[p].device_status != 0){
                            struct s_pumplink_control new_control;
                            new_control.dev_idx = p;
                            new_control.action = 2; // Desligar dispositivo
                            new_control.working_time = 0;
                            new_control.enabled = true;
                            new_control.start_time = now;
                            new_control.timeout = TIMER_5MIN;
                            pumplink_automation_state[i].canceled = true; // Perminte apenas um único cancelamento por automação
                            mesh_insert_pumplink_control(new_control);
                            ESP_LOGW(TAG_M, "[AUTO %d] LevelPump Turn Off Command dev_idx=%d", i, p);
                        }

                        if(pumplink_automation_state[i].working == true && pumplink_device_state[p].device_status == 0){ // Se a bomba estiver desligada
                            pumplink_automation_state[i].working = false;
                            report_automation_end(i, now);
                            ESP_LOGW(TAG_M, "[AUTO %d] End of Working to LevelPump dev_idx=%d", i, p);
                        }
                    }
                }
            }

            // Controle de dispositivo por comando
            for (int i = 0; i < MAX_MESH_CONTROL; i++) {
                int c = pumplink_control[i].dev_idx;
                if(pumplink_control[i].enabled && difftime(now, pumplink_device_state[c].last_sync) < TIMER_10MIN) {
                    if(difftime(now, pumplink_control[i].start_time) < pumplink_control[i].timeout){
                        char mesh_pkg[64];
                        int working_time = pumplink_control[i].working_time;

                        if(pumplink_control[i].action == 1){ // Ligar dispositivo
                            if (pumplink_device_state[c].device_status == 0) {
                                if ((now - pumplink_device_state[c].last_cmd) > TIMER_30S) { // 30 segundos
                                    pumplink_device_state[c].last_cmd = now;
                                    
                                    mesh_pumplink_turn_on_encode(pumplink_devices[c].meshid, pumplink_devices[c].device_id, pumplink_devices[c].out1, pumplink_devices[c].out2, pumplink_devices[c].input, pumplink_devices[c].mode, working_time, mesh_pkg);                              
                                    uart_tx_chars(UART_NUM, mesh_pkg, strlen(mesh_pkg));
                                    
                                    ESP_LOGI(TAG_M, "Device Turn ON");                    
                                    ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[CTRL %d] Sending control command to dev_idx=%d meshid=%d device_id=%d (working_time=%d)" ANSI_COLOR_RESET,
                                                i, c,
                                                pumplink_devices[c].meshid,
                                                pumplink_devices[c].device_id,
                                                working_time);
                                }
                            } else { // Se o dispositivo estiver ligado
                                pumplink_control[i].enabled = false;
                                ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[CTRL %d] dev_idx=%d is already on" ANSI_COLOR_RESET, i, c);
                            }

                        } else if(pumplink_control[i].action == 2) { // Desligar dispositivo
                            if (pumplink_device_state[c].device_status != 0) {
                                if ((now - pumplink_device_state[c].last_cmd) > TIMER_30S) { // 30 segundos
                                    pumplink_device_state[c].last_cmd = now;
                                    char out = pumplink_devices[c].out1;
                                    if((pumplink_devices[c].mode&PULSE) == PULSE){
                                        out = pumplink_devices[c].out2;
                                    }
                                    mesh_pumplink_turn_off_encode(pumplink_devices[c].meshid, pumplink_devices[c].device_id, out, pumplink_devices[c].mode, mesh_pkg);
                                    uart_tx_chars(UART_NUM, mesh_pkg, strlen(mesh_pkg));

                                    ESP_LOGI(TAG_M, "Device Turn OFF");                    
                                    ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[CTRL %d] Sending control command to dev_idx=%d meshid=%d device_id=%d (working_time=%d)" ANSI_COLOR_RESET,
                                                i, c,
                                                pumplink_devices[c].meshid,
                                                pumplink_devices[c].device_id,
                                                working_time);
                                }
                            } else { // Se o dispositivo estiver desligado
                                pumplink_control[i].enabled = false;
                                ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[CTRL %d] dev_idx=%d is already off" ANSI_COLOR_RESET, i, c);
                            }
                        }
                    } else {
                        pumplink_control[i].enabled = false;
                    }
                }
            }
        }

        // Verifica se há dados para processar
        if (ulTaskNotifyTake(pdTRUE, 0)) {
            ESP_LOGI(TAG_M, "Data received...");
            if (xSemaphoreTake(data_mutex, pdMS_TO_TICKS(10)) == pdTRUE) {
                char *result = NULL;
                int len = 0;
                process_json_data(&result, &len);
                if (result != NULL) {
                    if(protocol.origin == P_MQTT){
                        ESP_LOGI(TAG_M, "MQTT result len %d", len);
                        for (size_t i = 0; i < len; i++) {
                            printf("%02x ", result[i]);
                        }
                        printf("\n");
                        int res = esp_mqtt_client_enqueue(mqtt_client, publish_topic_report, result, len, 1, false, true);
                        if (res < 0) {
                            ESP_LOGW(TAG_M, "MQTT enqueue failed: %d", res);
                        } else {
                            ESP_LOGI(TAG_M, "MQTT enqueued msg_id=%d", res);
                        }
                    }
                }
                free(result);
                protocol.len = 0;
                xSemaphoreGive(data_mutex);
            }
        }
        
        // Dados da rede Mesh
        if (xQueueReceive(uart_queue, (void*)&event, pdMS_TO_TICKS(10))) {
            switch (event.type) {
                case UART_BUFFER_FULL:
                case UART_FIFO_OVF:
                    handle_uart_exception();
                break;                
                case UART_PATTERN_DET:
                    int pos = uart_pattern_pop_pos(UART_NUM);
                    if (pos != -1) {
                        int wanted = pos + 1;
                        int len = uart_read_bytes(UART_NUM, uart_data, wanted, pdMS_TO_TICKS(200));
                        if (len != wanted) {
                            handle_uart_exception();
                            break;
                        }

                        uart_data[pos]='}';
                        uart_data[pos+1]='\0';
                        ESP_LOGI(TAG_M, "Received from mesh: %s", uart_data);
                        if (strstr(uart_data, "{01;") != NULL){
                            if(mesh_pumplink_decode((uint8_t *)uart_data, strlen(uart_data), &pumplink_decode) == ESP_FAIL){
                                handle_uart_exception();
                            }
                        }
                        uart_data[0]='\0';
                    }else{
                        uart_flush_input(UART_NUM);
                        xQueueReset(uart_queue);
                    }

                    break;
                default:
                    break;
            }
        }

        // Acionamento da GPIO do Pluviometro
        if(xQueueReceive(gpio_evt_queue, &io_num, pdMS_TO_TICKS(10))) {
            if(pumplink_config.raingauge_enabled == true && difftime(now, last_gpio_interruption) >= 1){
                last_gpio_interruption = now;
                last_its_raining = now;
                rainfall_count[timeinfo.tm_hour] += 1;
                its_raining = true;
                update_rainfall_last24h();
                
                ESP_LOGI(TAG_M, "Rainfall Count[%d]: %d, mm: %d", timeinfo.tm_hour, rainfall_count[timeinfo.tm_hour], rainfall_last24h);
            }
        }

        // Verifica se o agendamento deve ser pausado devido a chuva
        if(pumplink_config.raingauge_enabled == true){
            if(current_rainfall_hour != timeinfo.tm_hour){
                current_rainfall_hour = timeinfo.tm_hour;
                rainfall_count[current_rainfall_hour] = 0;
                update_rainfall_last24h();
                ESP_LOGI(TAG_M, "Rainfall Count[%d]=0", current_rainfall_hour);
                printf("Rainfall DB: ");
                for (int i = 0; i < 24; ++i) {
                    printf("%d ", rainfall_count[i]);
                }
                printf("\n");
            }

            if(!pause_scheduling && ((rainfall_last24h >= pumplink_config.rainfall_limit && pumplink_config.rainfall_limit > 0))){
                pause_scheduling = true;
                pause_scheduling_time = now;
                pause_scheduling_timer = pumplink_config.rainfall_pause_duration*3600; // em horas
                ESP_LOGW(TAG_M, "Scheduling Paused for %dh due to rainfall", pumplink_config.rainfall_pause_duration);
            }

            if(its_raining == true && difftime(now, last_its_raining) >= 300){ // Se paasaram 5 minutos sem chuva
                its_raining = false;
            } 
        }

        // Verifica se o agendamento deve ser retomado por tempo
        if(pause_scheduling == true && difftime(now, pause_scheduling_time) > pause_scheduling_timer){
            pause_scheduling = false;
            ESP_LOGW(TAG_M, "Scheduling Resumed for time");
        }

        // Envio de relatórios
        mqtt_report();

        // Verifica se o watchdog deve ser reiniciado
        if(difftime(now, wdt_time) >= 1){
            wdt_time = now;
            esp_task_wdt_reset();
        }

        if(MEMORY_DEBUG && difftime(now, memory_debug_time) >= TIMER_30S){
            memory_debug_time = now;
            ESP_LOGI(TAG_M, "Free heap: %" PRIu32 ", Min heap: %" PRIu32 ", Largest block: %zu, "
                    "Stack watermark: %u, Task duration: %.4f ms (max: %.4f ms)", 
                    esp_get_free_heap_size(), 
                    esp_get_minimum_free_heap_size(), 
                    heap_caps_get_largest_free_block(MALLOC_CAP_8BIT), 
                    uxTaskGetStackHighWaterMark(NULL), 
                    task_duration, 
                    task_max_duration);
        }
        
        task_duration = (esp_timer_get_time() - task_start_loop) / 1000.0;
        if (task_duration > task_max_duration) {
            task_max_duration = task_duration;
        }

        vTaskDelay(pdMS_TO_TICKS(200));
    }
}

void app_main() {

    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_POSEDGE, // Change to GPIO_INTR_NEGEDGE or GPIO_INTR_ANYEDGE as needed
        .mode = GPIO_MODE_INPUT,
        .pin_bit_mask = (1ULL << GPIO_INPUT_IO_34),
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .pull_up_en = GPIO_PULLUP_DISABLE,
    };
    gpio_config(&io_conf);
    gpio_install_isr_service(ESP_INTR_FLAG_DEFAULT);
    gpio_isr_handler_add(GPIO_INPUT_IO_34, gpio_isr_handler, (void*) GPIO_INPUT_IO_34);
    gpio_evt_queue = xQueueCreate(10, sizeof(uint32_t));
    data_mutex = xSemaphoreCreateMutexStatic(&data_mutex_cb);
    configASSERT(data_mutex); 

    memory_nvs_init();

    esp_efuse_mac_get_default(mac);

    esp_err_t err = memory_load_system_info();
    if (err != ESP_OK) {
        ESP_LOGE(TAG_M, "Failed to load system info. err=0x%X", err);
    }     
    err = memory_load_wifi_credentials(sta_ssid, MAX_STA_SSID_SIZE, sta_pass, MAX_STA_PASS_SIZE);
    if (err != ESP_OK) {
        ESP_LOGE(TAG_M, "Failed to load config to memory. err=0x%X", err);
    } 
    err = memory_load_devices();
    if (err != ESP_OK) {
        ESP_LOGE(TAG_M, "Failed to load devices to memory. err=0x%X", err);
    } 
    err = memory_load_scheduling();
    if (err != ESP_OK) {
        ESP_LOGE(TAG_M, "Failed to load scheduling to memory. err=0x%X", err);
    } 
    err = memory_load_device_scheduling();
    if (err != ESP_OK) {
        ESP_LOGE(TAG_M, "Failed to load device scheduling to memory. err=0x%X", err);
    } 
    err = memory_load_automation();
    if (err != ESP_OK) {
        ESP_LOGE(TAG_M, "Failed to load automation to memory. err=0x%X", err);
    } 
    err = memory_load_config();
    if (err != ESP_OK) {
        ESP_LOGE(TAG_M, "Failed to load config to memory. err=0x%X", err);
    } 

    if(sta_ssid[0]==0x00 || sta_pass[0]==0x00){
        strncpy((char*)sta_ssid, WIFI_STA_SSID, MAX_STA_SSID_SIZE);
        strncpy((char*)sta_pass, WIFI_STA_PASS, MAX_STA_PASS_SIZE);
        memory_save_wifi_credentials(sta_ssid, sta_pass);
        ESP_LOGI(TAG_M, "Save default SSID and Password of STA");
    }
    
    esp_reset_reason_t reason = esp_reset_reason();
    ESP_LOGI(TAG_M, "Reset Reason: %d", reason);
    ESP_LOG_BUFFER_HEX("sta_ssid",sta_ssid, MAX_STA_SSID_SIZE);
    ESP_LOGI(TAG_M, "STA SSID: %s", sta_ssid);
    ESP_LOGI(TAG_M, "STA Password: %s", sta_pass);

    uart_init();

    wifi_init_softap_sta();
    
    webserver_init();
    
    mqtt_init();

    //set_initial_rtc();
    initialize_sntp();

    xTaskCreate(&main_task, "main_task", 8192, NULL, 5, &data_task_handle);

}

#ifndef MAIN_H
#define MAIN_H

#include "defines.h"

#include <stdint.h>
#include <string.h>
#include <ctype.h>
#include "esp_mac.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include "esp_wifi.h"
#include "esp_netif.h"
#include <sys/socket.h>
#include <netinet/in.h>
#include <driver/uart.h>
#include <driver/gpio.h> 
#include <unistd.h>
#include <time.h>
#include "esp_sntp.h"
#include <inttypes.h>

#endif // MAIN_H
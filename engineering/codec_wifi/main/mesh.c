#define MESH_GLOBAL
#include "mesh.h"

static const char *TAG_MESH = "MESH";
uint16_t device_count = 0;
uint16_t schedule_count = 0;

void mesh_insert_pumplink_control(struct s_pumplink_control new_control) 
{
    for (int i = 0; i < MAX_MESH_CONTROL; i++) {
        if (pumplink_control[i].dev_idx == new_control.dev_idx) {
            pumplink_control[i].dev_idx = 0;
            pumplink_control[i].action = 0;
            pumplink_control[i].start_time = 0;
            pumplink_control[i].working_time = 0;
            pumplink_control[i].enabled = false;
            pumplink_control[i].timeout = TIMER_3MIN;
            break;
        }
    }

    // Shift elements to the right
    for (int i = MAX_MESH_CONTROL - 1; i > 0; i--) {
        pumplink_control[i] = pumplink_control[i - 1];
    }

    // Insert new element at index 0
    pumplink_control[0] = new_control;
}

void mesh_pumplink_turn_on_encode(uint32_t id, uint8_t device_id, uint8_t out1, uint8_t out2, uint8_t input, uint8_t mode, uint16_t working_time, char *mesh_pkg)
{
    uint8_t outputs = (out2 << 4) | out1;
    uint8_t mesh_array[10] = {
        (id >> 16) & 0xFF,
        (id >> 8) & 0xFF,
        id & 0xFF,
        1,
        device_id,
        outputs,
        input,
        mode,
        (working_time >> 8) & 0xFF,
        working_time & 0xFF
    };

    char hex_string[21]; 
    for (int i = 0; i < 10; i++) {
        sprintf(&hex_string[i * 2], "%02X", mesh_array[i]);
    }

    sprintf(mesh_pkg, "{01;%s;", hex_string);

    uint16_t crc = crc16((uint8_t *)mesh_pkg, strlen(mesh_pkg));
    sprintf(mesh_pkg + strlen(mesh_pkg), "%04X}", crc);
}

void mesh_pumplink_turn_off_encode(uint32_t id, uint8_t device_id, uint8_t out, uint8_t mode, char *mesh_pkg) {
    uint8_t mesh_array[7] = {
        (id >> 16) & 0xFF,
        (id >> 8) & 0xFF,
        id & 0xFF,
        2,
        device_id,
        out,
        mode
    };

    char hex_string[15]; 
    for (int i = 0; i < 7; i++) {
        sprintf(&hex_string[i * 2], "%02X", mesh_array[i]);
    }

    sprintf(mesh_pkg, "{01;%s;", hex_string);

    uint16_t crc = crc16((uint8_t *)mesh_pkg, strlen(mesh_pkg));
    sprintf(mesh_pkg + strlen(mesh_pkg), "%04X}", crc);
}

esp_err_t mesh_pumplink_decode(uint8_t *bytes, size_t length, struct s_pumplink_decode *decode)
{
    char prefix[3];
    char payload[51];
    char crc[5];
    unsigned char data[26];
    
    int result = sscanf((const char *)bytes, "{%2[^;];%99[^;];%99[^}]}", prefix, payload, crc);

    if (length < 11 || result != 3) {
        return ESP_FAIL;
    }

    int size = strlen(payload);
    for(int i=0; i<size/2; i++){
        sscanf(payload + i * 2, "%02hhX", &data[i]);
    }

    uint32_t id = (data[0] << 16) | (data[1] << 8) | data[2];
    if (id != 0 && data[4] == 0x05) {
        struct timeval tv;
        gettimeofday(&tv, NULL);
        time_t now;
        time(&now);

        decode->meshid = id;
        decode->devices_working = data[5];
        decode->outputs = data[6];
        decode->inputs = data[7];
        decode->device_id = data[8] & 0x0F;
        decode->flags = (data[8] >> 4) & 0x0F;
        decode->time_left = (data[9] << 8) | data[10];
        decode->last_sync = now;

        ESP_LOGI(TAG_MESH, "meshid: %d, devices_working: %d, inputs: %d, outputs: %d, flags: %d, device_id: %d, time_left: %d, last_sync: %d",
         decode->meshid, decode->devices_working, decode->inputs, decode->outputs, decode->flags, decode->device_id, decode->time_left, decode->last_sync);

        for(int i=0; i<MAX_MESH_DEVICES; i++){
            if(pumplink_devices[i].meshid == decode->meshid){
                pumplink_device_state[i].id = decode->meshid;
                pumplink_device_state[i].device_status = decode->devices_working & (1 << pumplink_devices[i].device_id);
                pumplink_device_state[i].outputs = decode->outputs;
                pumplink_device_state[i].inputs = decode->inputs;
                pumplink_device_state[i].flags = decode->flags;
                if(pumplink_devices[i].device_id == decode->device_id){
                    pumplink_device_state[i].time_left = decode->time_left;
                }
                pumplink_device_state[i].last_sync = decode->last_sync;
            }
        }
        
        return ESP_OK;
    } else {
        return ESP_FAIL;
    }
}

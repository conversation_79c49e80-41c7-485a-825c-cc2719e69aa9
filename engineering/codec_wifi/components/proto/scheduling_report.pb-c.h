/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: scheduling_report.proto */

#ifndef PROTOBUF_C_scheduling_5freport_2eproto__INCLUDED
#define PROTOBUF_C_scheduling_5freport_2eproto__INCLUDED

#include <protobuf-c/protobuf-c.h>

PROTOBUF_C__BEGIN_DECLS

#if PROTOBUF_C_VERSION_NUMBER < 1003000
# error This file was generated by a newer version of protoc-c which is incompatible with your libprotobuf-c headers. Please update your headers.
#elif 1003003 < PROTOBUF_C_MIN_COMPILER_VERSION
# error This file was generated by an older version of protoc-c which is incompatible with your libprotobuf-c headers. Please regenerate this file with a newer version of protoc-c.
#endif


typedef struct _Codec__Out__SchedulingReport__SchedulingReportData Codec__Out__SchedulingReport__SchedulingReportData;
typedef struct _Codec__Out__SchedulingReport__SchedulingReportPackage Codec__Out__SchedulingReport__SchedulingReportPackage;


/* --- enums --- */


/* --- messages --- */

struct  _Codec__Out__SchedulingReport__SchedulingReportData
{
  ProtobufCMessage base;
  /*
   * Índice do agendamento
   */
  int32_t scheduling_idx;
  /*
   * Timestamp do início do agendamento
   */
  uint64_t start_time;
  /*
   * Timestamp do fim do agendamento
   */
  uint64_t end_time;
  /*
   * Bitmask de setores acionados, mascara com 64bits iniciais
   */
  uint64_t sector_bitmask1;
  /*
   * Bitmask de setores acionados, mascara com mais 64bits
   */
  uint64_t sector_bitmask2;
  /*
   * Bitmask da ferti de setores acionados, mascara com 64bits iniciais
   */
  uint64_t ferti_bitmask1;
  /*
   * Bitmask da ferti de setores acionados, mascara com mais 64bits
   */
  uint64_t ferti_bitmask2;
  /*
   * Estado da bomba de água
   */
  uint32_t waterpump;
  /*
   * Estado da retrolavagem
   */
  uint32_t backwash;
  /*
   * Timestamp de início da retrolavagem
   */
  uint64_t backwash_time;
  /*
   * Código de status do agendamento
   */
  int32_t status;
};
#define CODEC__OUT__SCHEDULING_REPORT__SCHEDULING_REPORT_DATA__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&codec__out__scheduling_report__scheduling_report_data__descriptor) \
    , 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 }


struct  _Codec__Out__SchedulingReport__SchedulingReportPackage
{
  ProtobufCMessage base;
  /*
   * Lista de relatórios
   */
  size_t n_data;
  Codec__Out__SchedulingReport__SchedulingReportData **data;
};
#define CODEC__OUT__SCHEDULING_REPORT__SCHEDULING_REPORT_PACKAGE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&codec__out__scheduling_report__scheduling_report_package__descriptor) \
    , 0,NULL }


/* Codec__Out__SchedulingReport__SchedulingReportData methods */
void   codec__out__scheduling_report__scheduling_report_data__init
                     (Codec__Out__SchedulingReport__SchedulingReportData         *message);
size_t codec__out__scheduling_report__scheduling_report_data__get_packed_size
                     (const Codec__Out__SchedulingReport__SchedulingReportData   *message);
size_t codec__out__scheduling_report__scheduling_report_data__pack
                     (const Codec__Out__SchedulingReport__SchedulingReportData   *message,
                      uint8_t             *out);
size_t codec__out__scheduling_report__scheduling_report_data__pack_to_buffer
                     (const Codec__Out__SchedulingReport__SchedulingReportData   *message,
                      ProtobufCBuffer     *buffer);
Codec__Out__SchedulingReport__SchedulingReportData *
       codec__out__scheduling_report__scheduling_report_data__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   codec__out__scheduling_report__scheduling_report_data__free_unpacked
                     (Codec__Out__SchedulingReport__SchedulingReportData *message,
                      ProtobufCAllocator *allocator);
/* Codec__Out__SchedulingReport__SchedulingReportPackage methods */
void   codec__out__scheduling_report__scheduling_report_package__init
                     (Codec__Out__SchedulingReport__SchedulingReportPackage         *message);
size_t codec__out__scheduling_report__scheduling_report_package__get_packed_size
                     (const Codec__Out__SchedulingReport__SchedulingReportPackage   *message);
size_t codec__out__scheduling_report__scheduling_report_package__pack
                     (const Codec__Out__SchedulingReport__SchedulingReportPackage   *message,
                      uint8_t             *out);
size_t codec__out__scheduling_report__scheduling_report_package__pack_to_buffer
                     (const Codec__Out__SchedulingReport__SchedulingReportPackage   *message,
                      ProtobufCBuffer     *buffer);
Codec__Out__SchedulingReport__SchedulingReportPackage *
       codec__out__scheduling_report__scheduling_report_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   codec__out__scheduling_report__scheduling_report_package__free_unpacked
                     (Codec__Out__SchedulingReport__SchedulingReportPackage *message,
                      ProtobufCAllocator *allocator);
/* --- per-message closures --- */

typedef void (*Codec__Out__SchedulingReport__SchedulingReportData_Closure)
                 (const Codec__Out__SchedulingReport__SchedulingReportData *message,
                  void *closure_data);
typedef void (*Codec__Out__SchedulingReport__SchedulingReportPackage_Closure)
                 (const Codec__Out__SchedulingReport__SchedulingReportPackage *message,
                  void *closure_data);

/* --- services --- */


/* --- descriptors --- */

extern const ProtobufCMessageDescriptor codec__out__scheduling_report__scheduling_report_data__descriptor;
extern const ProtobufCMessageDescriptor codec__out__scheduling_report__scheduling_report_package__descriptor;

PROTOBUF_C__END_DECLS


#endif  /* PROTOBUF_C_scheduling_5freport_2eproto__INCLUDED */

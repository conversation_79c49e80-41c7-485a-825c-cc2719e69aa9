/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: device_scheduling.proto */

#ifndef PROTOBUF_C_device_5fscheduling_2eproto__INCLUDED
#define PROTOBUF_C_device_5fscheduling_2eproto__INCLUDED

#include <protobuf-c/protobuf-c.h>

PROTOBUF_C__BEGIN_DECLS

#if PROTOBUF_C_VERSION_NUMBER < 1003000
# error This file was generated by a newer version of protoc-c which is incompatible with your libprotobuf-c headers. Please update your headers.
#elif 1003003 < PROTOBUF_C_MIN_COMPILER_VERSION
# error This file was generated by an older version of protoc-c which is incompatible with your libprotobuf-c headers. Please regenerate this file with a newer version of protoc-c.
#endif


typedef struct _Codec__In__DeviceScheduling__DeviceScheduling Codec__In__DeviceScheduling__DeviceScheduling;
typedef struct _Codec__In__DeviceScheduling__DeviceSchedulingPackage Codec__In__DeviceScheduling__DeviceSchedulingPackage;


/* --- enums --- */


/* --- messages --- */

struct  _Codec__In__DeviceScheduling__DeviceScheduling
{
  ProtobufCMessage base;
  /*
   * Índice do agendamento do dispositivo
   */
  int32_t idx;
  /*
   * Índice do agendamento principal
   */
  int32_t scheduling_idx;
  /*
   * Índice do dispositivo
   */
  int32_t device_idx;
  /*
   * Ordem de execução no agendamento
   */
  int32_t order;
  /*
   * Tempo de início relativo (minutos após início do agendamento)
   */
  int32_t sector_working_time;
  /*
   * Tempo de funcionamento para fertilização (em minutos)
   */
  int32_t ferti_working_time;
  /*
   * Atraso antes da fertilização (em minutos)
   */
  int32_t ferti_delay;
};
#define CODEC__IN__DEVICE_SCHEDULING__DEVICE_SCHEDULING__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&codec__in__device_scheduling__device_scheduling__descriptor) \
    , 0, 0, 0, 0, 0, 0, 0 }


struct  _Codec__In__DeviceScheduling__DeviceSchedulingPackage
{
  ProtobufCMessage base;
  /*
   * Lista de agendamentos de dispositivos
   */
  size_t n_data;
  Codec__In__DeviceScheduling__DeviceScheduling **data;
};
#define CODEC__IN__DEVICE_SCHEDULING__DEVICE_SCHEDULING_PACKAGE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&codec__in__device_scheduling__device_scheduling_package__descriptor) \
    , 0,NULL }


/* Codec__In__DeviceScheduling__DeviceScheduling methods */
void   codec__in__device_scheduling__device_scheduling__init
                     (Codec__In__DeviceScheduling__DeviceScheduling         *message);
size_t codec__in__device_scheduling__device_scheduling__get_packed_size
                     (const Codec__In__DeviceScheduling__DeviceScheduling   *message);
size_t codec__in__device_scheduling__device_scheduling__pack
                     (const Codec__In__DeviceScheduling__DeviceScheduling   *message,
                      uint8_t             *out);
size_t codec__in__device_scheduling__device_scheduling__pack_to_buffer
                     (const Codec__In__DeviceScheduling__DeviceScheduling   *message,
                      ProtobufCBuffer     *buffer);
Codec__In__DeviceScheduling__DeviceScheduling *
       codec__in__device_scheduling__device_scheduling__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   codec__in__device_scheduling__device_scheduling__free_unpacked
                     (Codec__In__DeviceScheduling__DeviceScheduling *message,
                      ProtobufCAllocator *allocator);
/* Codec__In__DeviceScheduling__DeviceSchedulingPackage methods */
void   codec__in__device_scheduling__device_scheduling_package__init
                     (Codec__In__DeviceScheduling__DeviceSchedulingPackage         *message);
size_t codec__in__device_scheduling__device_scheduling_package__get_packed_size
                     (const Codec__In__DeviceScheduling__DeviceSchedulingPackage   *message);
size_t codec__in__device_scheduling__device_scheduling_package__pack
                     (const Codec__In__DeviceScheduling__DeviceSchedulingPackage   *message,
                      uint8_t             *out);
size_t codec__in__device_scheduling__device_scheduling_package__pack_to_buffer
                     (const Codec__In__DeviceScheduling__DeviceSchedulingPackage   *message,
                      ProtobufCBuffer     *buffer);
Codec__In__DeviceScheduling__DeviceSchedulingPackage *
       codec__in__device_scheduling__device_scheduling_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   codec__in__device_scheduling__device_scheduling_package__free_unpacked
                     (Codec__In__DeviceScheduling__DeviceSchedulingPackage *message,
                      ProtobufCAllocator *allocator);
/* --- per-message closures --- */

typedef void (*Codec__In__DeviceScheduling__DeviceScheduling_Closure)
                 (const Codec__In__DeviceScheduling__DeviceScheduling *message,
                  void *closure_data);
typedef void (*Codec__In__DeviceScheduling__DeviceSchedulingPackage_Closure)
                 (const Codec__In__DeviceScheduling__DeviceSchedulingPackage *message,
                  void *closure_data);

/* --- services --- */


/* --- descriptors --- */

extern const ProtobufCMessageDescriptor codec__in__device_scheduling__device_scheduling__descriptor;
extern const ProtobufCMessageDescriptor codec__in__device_scheduling__device_scheduling_package__descriptor;

PROTOBUF_C__END_DECLS


#endif  /* PROTOBUF_C_device_5fscheduling_2eproto__INCLUDED */

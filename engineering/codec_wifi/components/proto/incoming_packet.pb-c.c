/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: incoming_packet.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "incoming_packet.pb-c.h"
void   codec__in__incoming_packet__init
                     (Codec__In__IncomingPacket         *message)
{
  static const Codec__In__IncomingPacket init_value = CODEC__IN__INCOMING_PACKET__INIT;
  *message = init_value;
}
size_t codec__in__incoming_packet__get_packed_size
                     (const Codec__In__IncomingPacket *message)
{
  assert(message->base.descriptor == &codec__in__incoming_packet__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t codec__in__incoming_packet__pack
                     (const Codec__In__IncomingPacket *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &codec__in__incoming_packet__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t codec__in__incoming_packet__pack_to_buffer
                     (const Codec__In__IncomingPacket *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &codec__in__incoming_packet__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Codec__In__IncomingPacket *
       codec__in__incoming_packet__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Codec__In__IncomingPacket *)
     protobuf_c_message_unpack (&codec__in__incoming_packet__descriptor,
                                allocator, len, data);
}
void   codec__in__incoming_packet__free_unpacked
                     (Codec__In__IncomingPacket *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &codec__in__incoming_packet__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor codec__in__incoming_packet__field_descriptors[10] =
{
  {
    "id",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT64,
    0,   /* quantifier_offset */
    offsetof(Codec__In__IncomingPacket, id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "config",
    2,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Codec__In__IncomingPacket, payload_case),
    offsetof(Codec__In__IncomingPacket, config),
    &codec__in__config__config_package__descriptor,
    NULL,
    0 | PROTOBUF_C_FIELD_FLAG_ONEOF,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "devices",
    3,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Codec__In__IncomingPacket, payload_case),
    offsetof(Codec__In__IncomingPacket, devices),
    &codec__in__devices__devices_package__descriptor,
    NULL,
    0 | PROTOBUF_C_FIELD_FLAG_ONEOF,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "scheduling",
    4,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Codec__In__IncomingPacket, payload_case),
    offsetof(Codec__In__IncomingPacket, scheduling),
    &codec__in__scheduling__scheduling_package__descriptor,
    NULL,
    0 | PROTOBUF_C_FIELD_FLAG_ONEOF,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dev_scheduling",
    5,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Codec__In__IncomingPacket, payload_case),
    offsetof(Codec__In__IncomingPacket, dev_scheduling),
    &codec__in__device_scheduling__device_scheduling_package__descriptor,
    NULL,
    0 | PROTOBUF_C_FIELD_FLAG_ONEOF,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "automation",
    6,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Codec__In__IncomingPacket, payload_case),
    offsetof(Codec__In__IncomingPacket, automation),
    &codec__in__automation__automation_package__descriptor,
    NULL,
    0 | PROTOBUF_C_FIELD_FLAG_ONEOF,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "control",
    7,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Codec__In__IncomingPacket, payload_case),
    offsetof(Codec__In__IncomingPacket, control),
    &codec__in__control__control_package__descriptor,
    NULL,
    0 | PROTOBUF_C_FIELD_FLAG_ONEOF,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "pause",
    8,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Codec__In__IncomingPacket, payload_case),
    offsetof(Codec__In__IncomingPacket, pause),
    &codec__in__pause__pause_scheduling_package__descriptor,
    NULL,
    0 | PROTOBUF_C_FIELD_FLAG_ONEOF,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "request_info",
    9,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Codec__In__IncomingPacket, payload_case),
    offsetof(Codec__In__IncomingPacket, request_info),
    &codec__in__request_info__request_info_package__descriptor,
    NULL,
    0 | PROTOBUF_C_FIELD_FLAG_ONEOF,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "firmware_update",
    10,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Codec__In__IncomingPacket, payload_case),
    offsetof(Codec__In__IncomingPacket, firmware_update),
    &codec__in__firmware_update__firmware_update_package__descriptor,
    NULL,
    0 | PROTOBUF_C_FIELD_FLAG_ONEOF,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned codec__in__incoming_packet__field_indices_by_name[] = {
  5,   /* field[5] = automation */
  1,   /* field[1] = config */
  6,   /* field[6] = control */
  4,   /* field[4] = dev_scheduling */
  2,   /* field[2] = devices */
  9,   /* field[9] = firmware_update */
  0,   /* field[0] = id */
  7,   /* field[7] = pause */
  8,   /* field[8] = request_info */
  3,   /* field[3] = scheduling */
};
static const ProtobufCIntRange codec__in__incoming_packet__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 10 }
};
const ProtobufCMessageDescriptor codec__in__incoming_packet__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "codec.in.IncomingPacket",
  "IncomingPacket",
  "Codec__In__IncomingPacket",
  "codec.in",
  sizeof(Codec__In__IncomingPacket),
  10,
  codec__in__incoming_packet__field_descriptors,
  codec__in__incoming_packet__field_indices_by_name,
  1,  codec__in__incoming_packet__number_ranges,
  (ProtobufCMessageInit) codec__in__incoming_packet__init,
  NULL,NULL,NULL    /* reserved[123] */
};

/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: outgoing_packet.proto */

#ifndef PROTOBUF_C_outgoing_5fpacket_2eproto__INCLUDED
#define PROTOBUF_C_outgoing_5fpacket_2eproto__INCLUDED

#include <protobuf-c/protobuf-c.h>

PROTOBUF_C__BEGIN_DECLS

#if PROTOBUF_C_VERSION_NUMBER < 1003000
# error This file was generated by a newer version of protoc-c which is incompatible with your libprotobuf-c headers. Please update your headers.
#elif 1003003 < PROTOBUF_C_MIN_COMPILER_VERSION
# error This file was generated by an older version of protoc-c which is incompatible with your libprotobuf-c headers. Please regenerate this file with a newer version of protoc-c.
#endif

#include "info.pb-c.h"
#include "status.pb-c.h"
#include "scheduling_report.pb-c.h"
#include "automation_report.pb-c.h"
#include "ack.pb-c.h"

typedef struct _Codec__Out__OutgoingPacket Codec__Out__OutgoingPacket;


/* --- enums --- */


/* --- messages --- */

typedef enum {
  CODEC__OUT__OUTGOING_PACKET__PAYLOAD__NOT_SET = 0,
  CODEC__OUT__OUTGOING_PACKET__PAYLOAD_INFO = 2,
  CODEC__OUT__OUTGOING_PACKET__PAYLOAD_STATUS = 3,
  CODEC__OUT__OUTGOING_PACKET__PAYLOAD_SCHEDULING_REPORT = 4,
  CODEC__OUT__OUTGOING_PACKET__PAYLOAD_AUTOMATION_REPORT = 5,
  CODEC__OUT__OUTGOING_PACKET__PAYLOAD_ACK = 6
    PROTOBUF_C__FORCE_ENUM_TO_BE_INT_SIZE(CODEC__OUT__OUTGOING_PACKET__PAYLOAD)
} Codec__Out__OutgoingPacket__PayloadCase;

struct  _Codec__Out__OutgoingPacket
{
  ProtobufCMessage base;
  uint64_t id;
  Codec__Out__OutgoingPacket__PayloadCase payload_case;
  union {
    Codec__Out__Info__InfoPackage *info;
    Codec__Out__Status__SystemStatusPackage *status;
    Codec__Out__SchedulingReport__SchedulingReportPackage *scheduling_report;
    Codec__Out__AutomationReport__AutomationReportPackage *automation_report;
    Codec__Out__Ack__AckPackage *ack;
  };
};
#define CODEC__OUT__OUTGOING_PACKET__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&codec__out__outgoing_packet__descriptor) \
    , 0, CODEC__OUT__OUTGOING_PACKET__PAYLOAD__NOT_SET, {0} }


/* Codec__Out__OutgoingPacket methods */
void   codec__out__outgoing_packet__init
                     (Codec__Out__OutgoingPacket         *message);
size_t codec__out__outgoing_packet__get_packed_size
                     (const Codec__Out__OutgoingPacket   *message);
size_t codec__out__outgoing_packet__pack
                     (const Codec__Out__OutgoingPacket   *message,
                      uint8_t             *out);
size_t codec__out__outgoing_packet__pack_to_buffer
                     (const Codec__Out__OutgoingPacket   *message,
                      ProtobufCBuffer     *buffer);
Codec__Out__OutgoingPacket *
       codec__out__outgoing_packet__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   codec__out__outgoing_packet__free_unpacked
                     (Codec__Out__OutgoingPacket *message,
                      ProtobufCAllocator *allocator);
/* --- per-message closures --- */

typedef void (*Codec__Out__OutgoingPacket_Closure)
                 (const Codec__Out__OutgoingPacket *message,
                  void *closure_data);

/* --- services --- */


/* --- descriptors --- */

extern const ProtobufCMessageDescriptor codec__out__outgoing_packet__descriptor;

PROTOBUF_C__END_DECLS


#endif  /* PROTOBUF_C_outgoing_5fpacket_2eproto__INCLUDED */

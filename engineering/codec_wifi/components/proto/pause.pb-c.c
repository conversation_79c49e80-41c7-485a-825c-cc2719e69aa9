/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: pause.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "pause.pb-c.h"
void   codec__in__pause__pause_scheduling_package__init
                     (Codec__In__Pause__PauseSchedulingPackage         *message)
{
  static const Codec__In__Pause__PauseSchedulingPackage init_value = CODEC__IN__PAUSE__PAUSE_SCHEDULING_PACKAGE__INIT;
  *message = init_value;
}
size_t codec__in__pause__pause_scheduling_package__get_packed_size
                     (const Codec__In__Pause__PauseSchedulingPackage *message)
{
  assert(message->base.descriptor == &codec__in__pause__pause_scheduling_package__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t codec__in__pause__pause_scheduling_package__pack
                     (const Codec__In__Pause__PauseSchedulingPackage *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &codec__in__pause__pause_scheduling_package__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t codec__in__pause__pause_scheduling_package__pack_to_buffer
                     (const Codec__In__Pause__PauseSchedulingPackage *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &codec__in__pause__pause_scheduling_package__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Codec__In__Pause__PauseSchedulingPackage *
       codec__in__pause__pause_scheduling_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Codec__In__Pause__PauseSchedulingPackage *)
     protobuf_c_message_unpack (&codec__in__pause__pause_scheduling_package__descriptor,
                                allocator, len, data);
}
void   codec__in__pause__pause_scheduling_package__free_unpacked
                     (Codec__In__Pause__PauseSchedulingPackage *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &codec__in__pause__pause_scheduling_package__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor codec__in__pause__pause_scheduling_package__field_descriptors[2] =
{
  {
    "state",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Pause__PauseSchedulingPackage, state),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "duration",
    2,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Pause__PauseSchedulingPackage, duration),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned codec__in__pause__pause_scheduling_package__field_indices_by_name[] = {
  1,   /* field[1] = duration */
  0,   /* field[0] = state */
};
static const ProtobufCIntRange codec__in__pause__pause_scheduling_package__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor codec__in__pause__pause_scheduling_package__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "codec.in.pause.PauseSchedulingPackage",
  "PauseSchedulingPackage",
  "Codec__In__Pause__PauseSchedulingPackage",
  "codec.in.pause",
  sizeof(Codec__In__Pause__PauseSchedulingPackage),
  2,
  codec__in__pause__pause_scheduling_package__field_descriptors,
  codec__in__pause__pause_scheduling_package__field_indices_by_name,
  1,  codec__in__pause__pause_scheduling_package__number_ranges,
  (ProtobufCMessageInit) codec__in__pause__pause_scheduling_package__init,
  NULL,NULL,NULL    /* reserved[123] */
};

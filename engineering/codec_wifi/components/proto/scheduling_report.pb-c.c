/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: scheduling_report.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "scheduling_report.pb-c.h"
void   codec__out__scheduling_report__scheduling_report_data__init
                     (Codec__Out__SchedulingReport__SchedulingReportData         *message)
{
  static const Codec__Out__SchedulingReport__SchedulingReportData init_value = CODEC__OUT__SCHEDULING_REPORT__SCHEDULING_REPORT_DATA__INIT;
  *message = init_value;
}
size_t codec__out__scheduling_report__scheduling_report_data__get_packed_size
                     (const Codec__Out__SchedulingReport__SchedulingReportData *message)
{
  assert(message->base.descriptor == &codec__out__scheduling_report__scheduling_report_data__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t codec__out__scheduling_report__scheduling_report_data__pack
                     (const Codec__Out__SchedulingReport__SchedulingReportData *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &codec__out__scheduling_report__scheduling_report_data__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t codec__out__scheduling_report__scheduling_report_data__pack_to_buffer
                     (const Codec__Out__SchedulingReport__SchedulingReportData *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &codec__out__scheduling_report__scheduling_report_data__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Codec__Out__SchedulingReport__SchedulingReportData *
       codec__out__scheduling_report__scheduling_report_data__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Codec__Out__SchedulingReport__SchedulingReportData *)
     protobuf_c_message_unpack (&codec__out__scheduling_report__scheduling_report_data__descriptor,
                                allocator, len, data);
}
void   codec__out__scheduling_report__scheduling_report_data__free_unpacked
                     (Codec__Out__SchedulingReport__SchedulingReportData *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &codec__out__scheduling_report__scheduling_report_data__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   codec__out__scheduling_report__scheduling_report_package__init
                     (Codec__Out__SchedulingReport__SchedulingReportPackage         *message)
{
  static const Codec__Out__SchedulingReport__SchedulingReportPackage init_value = CODEC__OUT__SCHEDULING_REPORT__SCHEDULING_REPORT_PACKAGE__INIT;
  *message = init_value;
}
size_t codec__out__scheduling_report__scheduling_report_package__get_packed_size
                     (const Codec__Out__SchedulingReport__SchedulingReportPackage *message)
{
  assert(message->base.descriptor == &codec__out__scheduling_report__scheduling_report_package__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t codec__out__scheduling_report__scheduling_report_package__pack
                     (const Codec__Out__SchedulingReport__SchedulingReportPackage *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &codec__out__scheduling_report__scheduling_report_package__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t codec__out__scheduling_report__scheduling_report_package__pack_to_buffer
                     (const Codec__Out__SchedulingReport__SchedulingReportPackage *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &codec__out__scheduling_report__scheduling_report_package__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Codec__Out__SchedulingReport__SchedulingReportPackage *
       codec__out__scheduling_report__scheduling_report_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Codec__Out__SchedulingReport__SchedulingReportPackage *)
     protobuf_c_message_unpack (&codec__out__scheduling_report__scheduling_report_package__descriptor,
                                allocator, len, data);
}
void   codec__out__scheduling_report__scheduling_report_package__free_unpacked
                     (Codec__Out__SchedulingReport__SchedulingReportPackage *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &codec__out__scheduling_report__scheduling_report_package__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor codec__out__scheduling_report__scheduling_report_data__field_descriptors[11] =
{
  {
    "scheduling_idx",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__SchedulingReport__SchedulingReportData, scheduling_idx),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "start_time",
    2,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT64,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__SchedulingReport__SchedulingReportData, start_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "end_time",
    3,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT64,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__SchedulingReport__SchedulingReportData, end_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sector_bitmask1",
    4,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT64,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__SchedulingReport__SchedulingReportData, sector_bitmask1),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sector_bitmask2",
    5,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT64,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__SchedulingReport__SchedulingReportData, sector_bitmask2),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ferti_bitmask1",
    6,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT64,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__SchedulingReport__SchedulingReportData, ferti_bitmask1),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ferti_bitmask2",
    7,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT64,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__SchedulingReport__SchedulingReportData, ferti_bitmask2),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "waterpump",
    8,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT32,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__SchedulingReport__SchedulingReportData, waterpump),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "backwash",
    9,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT32,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__SchedulingReport__SchedulingReportData, backwash),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "backwash_time",
    10,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT64,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__SchedulingReport__SchedulingReportData, backwash_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "status",
    11,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__SchedulingReport__SchedulingReportData, status),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned codec__out__scheduling_report__scheduling_report_data__field_indices_by_name[] = {
  8,   /* field[8] = backwash */
  9,   /* field[9] = backwash_time */
  2,   /* field[2] = end_time */
  5,   /* field[5] = ferti_bitmask1 */
  6,   /* field[6] = ferti_bitmask2 */
  0,   /* field[0] = scheduling_idx */
  3,   /* field[3] = sector_bitmask1 */
  4,   /* field[4] = sector_bitmask2 */
  1,   /* field[1] = start_time */
  10,   /* field[10] = status */
  7,   /* field[7] = waterpump */
};
static const ProtobufCIntRange codec__out__scheduling_report__scheduling_report_data__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 11 }
};
const ProtobufCMessageDescriptor codec__out__scheduling_report__scheduling_report_data__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "codec.out.scheduling_report.SchedulingReportData",
  "SchedulingReportData",
  "Codec__Out__SchedulingReport__SchedulingReportData",
  "codec.out.scheduling_report",
  sizeof(Codec__Out__SchedulingReport__SchedulingReportData),
  11,
  codec__out__scheduling_report__scheduling_report_data__field_descriptors,
  codec__out__scheduling_report__scheduling_report_data__field_indices_by_name,
  1,  codec__out__scheduling_report__scheduling_report_data__number_ranges,
  (ProtobufCMessageInit) codec__out__scheduling_report__scheduling_report_data__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor codec__out__scheduling_report__scheduling_report_package__field_descriptors[1] =
{
  {
    "data",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Codec__Out__SchedulingReport__SchedulingReportPackage, n_data),
    offsetof(Codec__Out__SchedulingReport__SchedulingReportPackage, data),
    &codec__out__scheduling_report__scheduling_report_data__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned codec__out__scheduling_report__scheduling_report_package__field_indices_by_name[] = {
  0,   /* field[0] = data */
};
static const ProtobufCIntRange codec__out__scheduling_report__scheduling_report_package__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor codec__out__scheduling_report__scheduling_report_package__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "codec.out.scheduling_report.SchedulingReportPackage",
  "SchedulingReportPackage",
  "Codec__Out__SchedulingReport__SchedulingReportPackage",
  "codec.out.scheduling_report",
  sizeof(Codec__Out__SchedulingReport__SchedulingReportPackage),
  1,
  codec__out__scheduling_report__scheduling_report_package__field_descriptors,
  codec__out__scheduling_report__scheduling_report_package__field_indices_by_name,
  1,  codec__out__scheduling_report__scheduling_report_package__number_ranges,
  (ProtobufCMessageInit) codec__out__scheduling_report__scheduling_report_package__init,
  NULL,NULL,NULL    /* reserved[123] */
};

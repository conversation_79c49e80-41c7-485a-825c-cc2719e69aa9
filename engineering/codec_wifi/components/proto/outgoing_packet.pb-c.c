/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: outgoing_packet.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "outgoing_packet.pb-c.h"
void   codec__out__outgoing_packet__init
                     (Codec__Out__OutgoingPacket         *message)
{
  static const Codec__Out__OutgoingPacket init_value = CODEC__OUT__OUTGOING_PACKET__INIT;
  *message = init_value;
}
size_t codec__out__outgoing_packet__get_packed_size
                     (const Codec__Out__OutgoingPacket *message)
{
  assert(message->base.descriptor == &codec__out__outgoing_packet__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t codec__out__outgoing_packet__pack
                     (const Codec__Out__OutgoingPacket *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &codec__out__outgoing_packet__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t codec__out__outgoing_packet__pack_to_buffer
                     (const Codec__Out__OutgoingPacket *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &codec__out__outgoing_packet__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Codec__Out__OutgoingPacket *
       codec__out__outgoing_packet__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Codec__Out__OutgoingPacket *)
     protobuf_c_message_unpack (&codec__out__outgoing_packet__descriptor,
                                allocator, len, data);
}
void   codec__out__outgoing_packet__free_unpacked
                     (Codec__Out__OutgoingPacket *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &codec__out__outgoing_packet__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor codec__out__outgoing_packet__field_descriptors[6] =
{
  {
    "id",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT64,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__OutgoingPacket, id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "info",
    2,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Codec__Out__OutgoingPacket, payload_case),
    offsetof(Codec__Out__OutgoingPacket, info),
    &codec__out__info__info_package__descriptor,
    NULL,
    0 | PROTOBUF_C_FIELD_FLAG_ONEOF,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "status",
    3,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Codec__Out__OutgoingPacket, payload_case),
    offsetof(Codec__Out__OutgoingPacket, status),
    &codec__out__status__system_status_package__descriptor,
    NULL,
    0 | PROTOBUF_C_FIELD_FLAG_ONEOF,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "scheduling_report",
    4,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Codec__Out__OutgoingPacket, payload_case),
    offsetof(Codec__Out__OutgoingPacket, scheduling_report),
    &codec__out__scheduling_report__scheduling_report_package__descriptor,
    NULL,
    0 | PROTOBUF_C_FIELD_FLAG_ONEOF,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "automation_report",
    5,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Codec__Out__OutgoingPacket, payload_case),
    offsetof(Codec__Out__OutgoingPacket, automation_report),
    &codec__out__automation_report__automation_report_package__descriptor,
    NULL,
    0 | PROTOBUF_C_FIELD_FLAG_ONEOF,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ack",
    6,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Codec__Out__OutgoingPacket, payload_case),
    offsetof(Codec__Out__OutgoingPacket, ack),
    &codec__out__ack__ack_package__descriptor,
    NULL,
    0 | PROTOBUF_C_FIELD_FLAG_ONEOF,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned codec__out__outgoing_packet__field_indices_by_name[] = {
  5,   /* field[5] = ack */
  4,   /* field[4] = automation_report */
  0,   /* field[0] = id */
  1,   /* field[1] = info */
  3,   /* field[3] = scheduling_report */
  2,   /* field[2] = status */
};
static const ProtobufCIntRange codec__out__outgoing_packet__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 6 }
};
const ProtobufCMessageDescriptor codec__out__outgoing_packet__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "codec.out.OutgoingPacket",
  "OutgoingPacket",
  "Codec__Out__OutgoingPacket",
  "codec.out",
  sizeof(Codec__Out__OutgoingPacket),
  6,
  codec__out__outgoing_packet__field_descriptors,
  codec__out__outgoing_packet__field_indices_by_name,
  1,  codec__out__outgoing_packet__number_ranges,
  (ProtobufCMessageInit) codec__out__outgoing_packet__init,
  NULL,NULL,NULL    /* reserved[123] */
};

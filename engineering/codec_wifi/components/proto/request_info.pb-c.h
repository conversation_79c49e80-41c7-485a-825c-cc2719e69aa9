/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: request_info.proto */

#ifndef PROTOBUF_C_request_5finfo_2eproto__INCLUDED
#define PROTOBUF_C_request_5finfo_2eproto__INCLUDED

#include <protobuf-c/protobuf-c.h>

PROTOBUF_C__BEGIN_DECLS

#if PROTOBUF_C_VERSION_NUMBER < 1003000
# error This file was generated by a newer version of protoc-c which is incompatible with your libprotobuf-c headers. Please update your headers.
#elif 1003003 < PROTOBUF_C_MIN_COMPILER_VERSION
# error This file was generated by an older version of protoc-c which is incompatible with your libprotobuf-c headers. Please regenerate this file with a newer version of protoc-c.
#endif


typedef struct _Codec__In__RequestInfo__RequestInfoPackage Codec__In__RequestInfo__RequestInfoPackage;


/* --- enums --- */


/* --- messages --- */

struct  _Codec__In__RequestInfo__RequestInfoPackage
{
  ProtobufCMessage base;
  /*
   * Tipo de dados a ser retornados
   */
  int32_t type;
};
#define CODEC__IN__REQUEST_INFO__REQUEST_INFO_PACKAGE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&codec__in__request_info__request_info_package__descriptor) \
    , 0 }


/* Codec__In__RequestInfo__RequestInfoPackage methods */
void   codec__in__request_info__request_info_package__init
                     (Codec__In__RequestInfo__RequestInfoPackage         *message);
size_t codec__in__request_info__request_info_package__get_packed_size
                     (const Codec__In__RequestInfo__RequestInfoPackage   *message);
size_t codec__in__request_info__request_info_package__pack
                     (const Codec__In__RequestInfo__RequestInfoPackage   *message,
                      uint8_t             *out);
size_t codec__in__request_info__request_info_package__pack_to_buffer
                     (const Codec__In__RequestInfo__RequestInfoPackage   *message,
                      ProtobufCBuffer     *buffer);
Codec__In__RequestInfo__RequestInfoPackage *
       codec__in__request_info__request_info_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   codec__in__request_info__request_info_package__free_unpacked
                     (Codec__In__RequestInfo__RequestInfoPackage *message,
                      ProtobufCAllocator *allocator);
/* --- per-message closures --- */

typedef void (*Codec__In__RequestInfo__RequestInfoPackage_Closure)
                 (const Codec__In__RequestInfo__RequestInfoPackage *message,
                  void *closure_data);

/* --- services --- */


/* --- descriptors --- */

extern const ProtobufCMessageDescriptor codec__in__request_info__request_info_package__descriptor;

PROTOBUF_C__END_DECLS


#endif  /* PROTOBUF_C_request_5finfo_2eproto__INCLUDED */

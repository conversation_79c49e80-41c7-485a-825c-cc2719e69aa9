/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: devices.proto */

#ifndef PROTOBUF_C_devices_2eproto__INCLUDED
#define PROTOBUF_C_devices_2eproto__INCLUDED

#include <protobuf-c/protobuf-c.h>

PROTOBUF_C__BEGIN_DECLS

#if PROTOBUF_C_VERSION_NUMBER < 1003000
# error This file was generated by a newer version of protoc-c which is incompatible with your libprotobuf-c headers. Please update your headers.
#elif 1003003 < PROTOBUF_C_MIN_COMPILER_VERSION
# error This file was generated by an older version of protoc-c which is incompatible with your libprotobuf-c headers. Please regenerate this file with a newer version of protoc-c.
#endif


typedef struct _Codec__In__Devices__DevicesData Codec__In__Devices__DevicesData;
typedef struct _Codec__In__Devices__DevicesPackage Codec__In__Devices__DevicesPackage;


/* --- enums --- */


/* --- messages --- */

struct  _Codec__In__Devices__DevicesData
{
  ProtobufCMessage base;
  /*
   * index in the array (device slot)
   */
  int32_t idx;
  /*
   * mesh ID
   */
  int32_t mesh_id;
  /*
   * device ID
   */
  int32_t device_id;
  /*
   * device type
   */
  int32_t device_type;
  /*
   * output 1 state
   */
  int32_t out1;
  /*
   * output 2 state
   */
  int32_t out2;
  /*
   * input state
   */
  int32_t input;
  /*
   * operating mode
   */
  int32_t mode;
  /*
   * sector ID
   */
  int32_t sector;
  /*
   * group index
   */
  int32_t group_idx;
  /*
   * equipment version
   */
  int32_t eqpt_ver;
};
#define CODEC__IN__DEVICES__DEVICES_DATA__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&codec__in__devices__devices_data__descriptor) \
    , 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 }


struct  _Codec__In__Devices__DevicesPackage
{
  ProtobufCMessage base;
  /*
   * list of mesh devices
   */
  size_t n_data;
  Codec__In__Devices__DevicesData **data;
};
#define CODEC__IN__DEVICES__DEVICES_PACKAGE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&codec__in__devices__devices_package__descriptor) \
    , 0,NULL }


/* Codec__In__Devices__DevicesData methods */
void   codec__in__devices__devices_data__init
                     (Codec__In__Devices__DevicesData         *message);
size_t codec__in__devices__devices_data__get_packed_size
                     (const Codec__In__Devices__DevicesData   *message);
size_t codec__in__devices__devices_data__pack
                     (const Codec__In__Devices__DevicesData   *message,
                      uint8_t             *out);
size_t codec__in__devices__devices_data__pack_to_buffer
                     (const Codec__In__Devices__DevicesData   *message,
                      ProtobufCBuffer     *buffer);
Codec__In__Devices__DevicesData *
       codec__in__devices__devices_data__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   codec__in__devices__devices_data__free_unpacked
                     (Codec__In__Devices__DevicesData *message,
                      ProtobufCAllocator *allocator);
/* Codec__In__Devices__DevicesPackage methods */
void   codec__in__devices__devices_package__init
                     (Codec__In__Devices__DevicesPackage         *message);
size_t codec__in__devices__devices_package__get_packed_size
                     (const Codec__In__Devices__DevicesPackage   *message);
size_t codec__in__devices__devices_package__pack
                     (const Codec__In__Devices__DevicesPackage   *message,
                      uint8_t             *out);
size_t codec__in__devices__devices_package__pack_to_buffer
                     (const Codec__In__Devices__DevicesPackage   *message,
                      ProtobufCBuffer     *buffer);
Codec__In__Devices__DevicesPackage *
       codec__in__devices__devices_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   codec__in__devices__devices_package__free_unpacked
                     (Codec__In__Devices__DevicesPackage *message,
                      ProtobufCAllocator *allocator);
/* --- per-message closures --- */

typedef void (*Codec__In__Devices__DevicesData_Closure)
                 (const Codec__In__Devices__DevicesData *message,
                  void *closure_data);
typedef void (*Codec__In__Devices__DevicesPackage_Closure)
                 (const Codec__In__Devices__DevicesPackage *message,
                  void *closure_data);

/* --- services --- */


/* --- descriptors --- */

extern const ProtobufCMessageDescriptor codec__in__devices__devices_data__descriptor;
extern const ProtobufCMessageDescriptor codec__in__devices__devices_package__descriptor;

PROTOBUF_C__END_DECLS


#endif  /* PROTOBUF_C_devices_2eproto__INCLUDED */

/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: config.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "config.pb-c.h"
void   codec__in__config__wifi_config__init
                     (Codec__In__Config__WifiConfig         *message)
{
  static const Codec__In__Config__WifiConfig init_value = CODEC__IN__CONFIG__WIFI_CONFIG__INIT;
  *message = init_value;
}
size_t codec__in__config__wifi_config__get_packed_size
                     (const Codec__In__Config__WifiConfig *message)
{
  assert(message->base.descriptor == &codec__in__config__wifi_config__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t codec__in__config__wifi_config__pack
                     (const Codec__In__Config__WifiConfig *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &codec__in__config__wifi_config__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t codec__in__config__wifi_config__pack_to_buffer
                     (const Codec__In__Config__WifiConfig *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &codec__in__config__wifi_config__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Codec__In__Config__WifiConfig *
       codec__in__config__wifi_config__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Codec__In__Config__WifiConfig *)
     protobuf_c_message_unpack (&codec__in__config__wifi_config__descriptor,
                                allocator, len, data);
}
void   codec__in__config__wifi_config__free_unpacked
                     (Codec__In__Config__WifiConfig *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &codec__in__config__wifi_config__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   codec__in__config__mesh_config__init
                     (Codec__In__Config__MeshConfig         *message)
{
  static const Codec__In__Config__MeshConfig init_value = CODEC__IN__CONFIG__MESH_CONFIG__INIT;
  *message = init_value;
}
size_t codec__in__config__mesh_config__get_packed_size
                     (const Codec__In__Config__MeshConfig *message)
{
  assert(message->base.descriptor == &codec__in__config__mesh_config__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t codec__in__config__mesh_config__pack
                     (const Codec__In__Config__MeshConfig *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &codec__in__config__mesh_config__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t codec__in__config__mesh_config__pack_to_buffer
                     (const Codec__In__Config__MeshConfig *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &codec__in__config__mesh_config__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Codec__In__Config__MeshConfig *
       codec__in__config__mesh_config__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Codec__In__Config__MeshConfig *)
     protobuf_c_message_unpack (&codec__in__config__mesh_config__descriptor,
                                allocator, len, data);
}
void   codec__in__config__mesh_config__free_unpacked
                     (Codec__In__Config__MeshConfig *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &codec__in__config__mesh_config__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   codec__in__config__config_package__init
                     (Codec__In__Config__ConfigPackage         *message)
{
  static const Codec__In__Config__ConfigPackage init_value = CODEC__IN__CONFIG__CONFIG_PACKAGE__INIT;
  *message = init_value;
}
size_t codec__in__config__config_package__get_packed_size
                     (const Codec__In__Config__ConfigPackage *message)
{
  assert(message->base.descriptor == &codec__in__config__config_package__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t codec__in__config__config_package__pack
                     (const Codec__In__Config__ConfigPackage *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &codec__in__config__config_package__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t codec__in__config__config_package__pack_to_buffer
                     (const Codec__In__Config__ConfigPackage *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &codec__in__config__config_package__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Codec__In__Config__ConfigPackage *
       codec__in__config__config_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Codec__In__Config__ConfigPackage *)
     protobuf_c_message_unpack (&codec__in__config__config_package__descriptor,
                                allocator, len, data);
}
void   codec__in__config__config_package__free_unpacked
                     (Codec__In__Config__ConfigPackage *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &codec__in__config__config_package__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor codec__in__config__wifi_config__field_descriptors[2] =
{
  {
    "ssid",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Config__WifiConfig, ssid),
    NULL,
    &protobuf_c_empty_string,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "password",
    2,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Config__WifiConfig, password),
    NULL,
    &protobuf_c_empty_string,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned codec__in__config__wifi_config__field_indices_by_name[] = {
  1,   /* field[1] = password */
  0,   /* field[0] = ssid */
};
static const ProtobufCIntRange codec__in__config__wifi_config__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor codec__in__config__wifi_config__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "codec.in.config.WifiConfig",
  "WifiConfig",
  "Codec__In__Config__WifiConfig",
  "codec.in.config",
  sizeof(Codec__In__Config__WifiConfig),
  2,
  codec__in__config__wifi_config__field_descriptors,
  codec__in__config__wifi_config__field_indices_by_name,
  1,  codec__in__config__wifi_config__number_ranges,
  (ProtobufCMessageInit) codec__in__config__wifi_config__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor codec__in__config__mesh_config__field_descriptors[2] =
{
  {
    "key",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_BYTES,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Config__MeshConfig, key),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "channel",
    2,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Config__MeshConfig, channel),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned codec__in__config__mesh_config__field_indices_by_name[] = {
  1,   /* field[1] = channel */
  0,   /* field[0] = key */
};
static const ProtobufCIntRange codec__in__config__mesh_config__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor codec__in__config__mesh_config__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "codec.in.config.MeshConfig",
  "MeshConfig",
  "Codec__In__Config__MeshConfig",
  "codec.in.config",
  sizeof(Codec__In__Config__MeshConfig),
  2,
  codec__in__config__mesh_config__field_descriptors,
  codec__in__config__mesh_config__field_indices_by_name,
  1,  codec__in__config__mesh_config__number_ranges,
  (ProtobufCMessageInit) codec__in__config__mesh_config__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor codec__in__config__config_package__field_descriptors[9] =
{
  {
    "backwash_cycle",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Config__ConfigPackage, backwash_cycle),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "backwash_duration",
    2,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Config__ConfigPackage, backwash_duration),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "backwash_delay",
    3,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Config__ConfigPackage, backwash_delay),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "raingauge_enabled",
    4,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_BOOL,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Config__ConfigPackage, raingauge_enabled),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "raingauge_factor",
    5,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Config__ConfigPackage, raingauge_factor),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "rainfall_limit",
    6,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Config__ConfigPackage, rainfall_limit),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "rainfall_pause_duration",
    7,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Config__ConfigPackage, rainfall_pause_duration),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "wifi",
    8,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Config__ConfigPackage, wifi),
    &codec__in__config__wifi_config__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "mesh",
    9,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Config__ConfigPackage, mesh),
    &codec__in__config__mesh_config__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned codec__in__config__config_package__field_indices_by_name[] = {
  0,   /* field[0] = backwash_cycle */
  2,   /* field[2] = backwash_delay */
  1,   /* field[1] = backwash_duration */
  8,   /* field[8] = mesh */
  5,   /* field[5] = rainfall_limit */
  6,   /* field[6] = rainfall_pause_duration */
  3,   /* field[3] = raingauge_enabled */
  4,   /* field[4] = raingauge_factor */
  7,   /* field[7] = wifi */
};
static const ProtobufCIntRange codec__in__config__config_package__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 9 }
};
const ProtobufCMessageDescriptor codec__in__config__config_package__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "codec.in.config.ConfigPackage",
  "ConfigPackage",
  "Codec__In__Config__ConfigPackage",
  "codec.in.config",
  sizeof(Codec__In__Config__ConfigPackage),
  9,
  codec__in__config__config_package__field_descriptors,
  codec__in__config__config_package__field_indices_by_name,
  1,  codec__in__config__config_package__number_ranges,
  (ProtobufCMessageInit) codec__in__config__config_package__init,
  NULL,NULL,NULL    /* reserved[123] */
};

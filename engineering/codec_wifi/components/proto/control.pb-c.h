/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: control.proto */

#ifndef PROTOBUF_C_control_2eproto__INCLUDED
#define PROTOBUF_C_control_2eproto__INCLUDED

#include <protobuf-c/protobuf-c.h>

PROTOBUF_C__BEGIN_DECLS

#if PROTOBUF_C_VERSION_NUMBER < 1003000
# error This file was generated by a newer version of protoc-c which is incompatible with your libprotobuf-c headers. Please update your headers.
#elif 1003003 < PROTOBUF_C_MIN_COMPILER_VERSION
# error This file was generated by an older version of protoc-c which is incompatible with your libprotobuf-c headers. Please regenerate this file with a newer version of protoc-c.
#endif


typedef struct _Codec__In__Control__ControlPackage Codec__In__Control__ControlPackage;


/* --- enums --- */

typedef enum _Codec__In__Control__MsgAction {
  CODEC__IN__CONTROL__MSG_ACTION__MSG_NONE = 0,
  CODEC__IN__CONTROL__MSG_ACTION__MSG_TURN_ON = 1,
  CODEC__IN__CONTROL__MSG_ACTION__MSG_TURN_OFF = 2
    PROTOBUF_C__FORCE_ENUM_TO_BE_INT_SIZE(CODEC__IN__CONTROL__MSG_ACTION)
} Codec__In__Control__MsgAction;

/* --- messages --- */

struct  _Codec__In__Control__ControlPackage
{
  ProtobufCMessage base;
  /*
   * Índice do dispositivo
   */
  int32_t idx;
  /*
   * Código da ação (ex: 1 = ligar, 2 = desligar)
   */
  Codec__In__Control__MsgAction action;
  /*
   * Tempo de funcionamento (em minutos)
   */
  int32_t working_time;
};
#define CODEC__IN__CONTROL__CONTROL_PACKAGE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&codec__in__control__control_package__descriptor) \
    , 0, CODEC__IN__CONTROL__MSG_ACTION__MSG_NONE, 0 }


/* Codec__In__Control__ControlPackage methods */
void   codec__in__control__control_package__init
                     (Codec__In__Control__ControlPackage         *message);
size_t codec__in__control__control_package__get_packed_size
                     (const Codec__In__Control__ControlPackage   *message);
size_t codec__in__control__control_package__pack
                     (const Codec__In__Control__ControlPackage   *message,
                      uint8_t             *out);
size_t codec__in__control__control_package__pack_to_buffer
                     (const Codec__In__Control__ControlPackage   *message,
                      ProtobufCBuffer     *buffer);
Codec__In__Control__ControlPackage *
       codec__in__control__control_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   codec__in__control__control_package__free_unpacked
                     (Codec__In__Control__ControlPackage *message,
                      ProtobufCAllocator *allocator);
/* --- per-message closures --- */

typedef void (*Codec__In__Control__ControlPackage_Closure)
                 (const Codec__In__Control__ControlPackage *message,
                  void *closure_data);

/* --- services --- */


/* --- descriptors --- */

extern const ProtobufCEnumDescriptor    codec__in__control__msg_action__descriptor;
extern const ProtobufCMessageDescriptor codec__in__control__control_package__descriptor;

PROTOBUF_C__END_DECLS


#endif  /* PROTOBUF_C_control_2eproto__INCLUDED */

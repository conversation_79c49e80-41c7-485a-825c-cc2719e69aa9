/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: automation.proto */

#ifndef PROTOBUF_C_automation_2eproto__INCLUDED
#define PROTOBUF_C_automation_2eproto__INCLUDED

#include <protobuf-c/protobuf-c.h>

PROTOBUF_C__BEGIN_DECLS

#if PROTOBUF_C_VERSION_NUMBER < 1003000
# error This file was generated by a newer version of protoc-c which is incompatible with your libprotobuf-c headers. Please update your headers.
#elif 1003003 < PROTOBUF_C_MIN_COMPILER_VERSION
# error This file was generated by an older version of protoc-c which is incompatible with your libprotobuf-c headers. Please regenerate this file with a newer version of protoc-c.
#endif


typedef struct _Codec__In__Automation__AutomationData Codec__In__Automation__AutomationData;
typedef struct _Codec__In__Automation__AutomationPackage Codec__In__Automation__AutomationPackage;


/* --- enums --- */


/* --- messages --- */

struct  _Codec__In__Automation__AutomationData
{
  ProtobufCMessage base;
  /*
   * level input (sensor de nível)
   */
  int32_t level_idx;
  /*
   * pump index
   */
  int32_t pump_idx;
  /*
   * mascara
   */
  int32_t mask;
  /*
   * valor de acionamento
   */
  int32_t value;
  /*
   * working time (tempo de acionamento)
   */
  int32_t working_time;
};
#define CODEC__IN__AUTOMATION__AUTOMATION_DATA__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&codec__in__automation__automation_data__descriptor) \
    , 0, 0, 0, 0, 0 }


struct  _Codec__In__Automation__AutomationPackage
{
  ProtobufCMessage base;
  /*
   * Lista de acionamentos
   */
  size_t n_data;
  Codec__In__Automation__AutomationData **data;
};
#define CODEC__IN__AUTOMATION__AUTOMATION_PACKAGE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&codec__in__automation__automation_package__descriptor) \
    , 0,NULL }


/* Codec__In__Automation__AutomationData methods */
void   codec__in__automation__automation_data__init
                     (Codec__In__Automation__AutomationData         *message);
size_t codec__in__automation__automation_data__get_packed_size
                     (const Codec__In__Automation__AutomationData   *message);
size_t codec__in__automation__automation_data__pack
                     (const Codec__In__Automation__AutomationData   *message,
                      uint8_t             *out);
size_t codec__in__automation__automation_data__pack_to_buffer
                     (const Codec__In__Automation__AutomationData   *message,
                      ProtobufCBuffer     *buffer);
Codec__In__Automation__AutomationData *
       codec__in__automation__automation_data__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   codec__in__automation__automation_data__free_unpacked
                     (Codec__In__Automation__AutomationData *message,
                      ProtobufCAllocator *allocator);
/* Codec__In__Automation__AutomationPackage methods */
void   codec__in__automation__automation_package__init
                     (Codec__In__Automation__AutomationPackage         *message);
size_t codec__in__automation__automation_package__get_packed_size
                     (const Codec__In__Automation__AutomationPackage   *message);
size_t codec__in__automation__automation_package__pack
                     (const Codec__In__Automation__AutomationPackage   *message,
                      uint8_t             *out);
size_t codec__in__automation__automation_package__pack_to_buffer
                     (const Codec__In__Automation__AutomationPackage   *message,
                      ProtobufCBuffer     *buffer);
Codec__In__Automation__AutomationPackage *
       codec__in__automation__automation_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   codec__in__automation__automation_package__free_unpacked
                     (Codec__In__Automation__AutomationPackage *message,
                      ProtobufCAllocator *allocator);
/* --- per-message closures --- */

typedef void (*Codec__In__Automation__AutomationData_Closure)
                 (const Codec__In__Automation__AutomationData *message,
                  void *closure_data);
typedef void (*Codec__In__Automation__AutomationPackage_Closure)
                 (const Codec__In__Automation__AutomationPackage *message,
                  void *closure_data);

/* --- services --- */


/* --- descriptors --- */

extern const ProtobufCMessageDescriptor codec__in__automation__automation_data__descriptor;
extern const ProtobufCMessageDescriptor codec__in__automation__automation_package__descriptor;

PROTOBUF_C__END_DECLS


#endif  /* PROTOBUF_C_automation_2eproto__INCLUDED */

<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.ConfigFragment">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="10dp">

            <Button
                android:id="@+id/frag_config_button_profile"
                android:layout_width="260dp"
                android:layout_height="wrap_content"
                android:text="Cadastro do Usuário"
                android:layout_marginTop="30dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <Button
                android:id="@+id/frag_config_button_codecs"
                android:layout_width="260dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="Cadastro de Dispositivos"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/frag_config_button_profile" />

            <Button
                android:id="@+id/frag_config_button_devices"
                android:layout_width="260dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="Acionamento"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/frag_config_button_codecs" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/frag_config_text_input_layout_backwash_cycle"
                android:layout_width="250dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                app:boxStrokeColor="@color/black"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/frag_config_button_devices">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/frag_config_text_input_backwash_cycle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@android:color/transparent"
                    android:digits="0123456789"
                    android:text="120"
                    android:hint="Ciclo da Retro"
                    android:inputType="numberDecimal" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/frag_config_text_input_layout_backwash_duration"
                android:layout_width="250dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                app:boxStrokeColor="@color/black"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/frag_config_text_input_layout_backwash_cycle">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/frag_config_text_input_backwash_duration"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@android:color/transparent"
                    android:digits="0123456789"
                    android:text="2"
                    android:hint="Duração da Retro"
                    android:inputType="numberDecimal" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/frag_config_text_input_layout_ferti_wash"
                android:layout_width="250dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                app:boxStrokeColor="@color/black"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/frag_config_text_input_layout_backwash_duration">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/frag_config_text_input_ferti_wash"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@android:color/transparent"
                    android:digits="0123456789"
                    android:text="1"
                    android:hint="Lavagem da Ferti"
                    android:inputType="numberDecimal" />
            </com.google.android.material.textfield.TextInputLayout>

            <Switch
                android:id="@+id/frag_config_switch_raingauge_enable"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="Pluviômetro"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.262"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/frag_config_text_input_layout_ferti_wash" />

            <LinearLayout
                android:id="@+id/frag_config_linear_layout_raingauge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center_horizontal"
                android:layout_marginTop="10dp"
                app:layout_constraintTop_toBottomOf="@id/frag_config_switch_raingauge_enable"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/frag_config_text_input_layout_raingauge_resolution"
                    android:layout_width="250dp"
                    android:layout_height="wrap_content"
                    app:boxStrokeColor="@color/black">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/frag_config_text_input_raingauge_resolution"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@android:color/transparent"
                        android:digits="0123456789."
                        android:hint="Resolução do Pluviômetro"
                        android:inputType="number"
                        android:text="0.2" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/frag_config_text_input_layout_rainfall_limit"
                    android:layout_width="250dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    app:boxStrokeColor="@color/black">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/frag_config_text_input_rainfall_limit"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@android:color/transparent"
                        android:digits="0123456789"
                        android:hint="Precipitação (chuva em mm)"
                        android:inputType="numberDecimal"
                        android:text="2" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/frag_config_text_input_layout_rainfall_pause_duration"
                    android:layout_width="250dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="20dp"
                    app:boxStrokeColor="@color/black">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/frag_config_text_input_rainfall_pause_duration"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@android:color/transparent"
                        android:digits="0123456789"
                        android:hint="Duração da Pausa (horas)"
                        android:inputType="numberDecimal"
                        android:text="24" />
                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>
</FrameLayout>
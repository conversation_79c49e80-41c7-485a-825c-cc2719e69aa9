<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="6dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="6dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/item_text_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="Agendamento da Banana"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="#222222"
                android:maxLines="1"
                android:ellipsize="end"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@id/item_text_date"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent" />

            <TextView
                android:id="@+id/item_text_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="25/04/2025 14:30"
                android:textSize="14sp"
                android:textColor="#444444"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBaseline_toBaselineOf="@id/item_text_title" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="12dp">

            <ImageView
                android:id="@+id/item_icon_sectors"
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:src="@drawable/ic_irrigacao"
                app:tint="@android:color/holo_blue_dark" />

            <TextView
                android:id="@+id/item_text_sectors"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Irrigação Completa"
                android:textSize="16sp"
                android:layout_marginStart="8dp"
                android:textColor="#444444" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="8dp">

            <ImageView
                android:id="@+id/item_icon_ferti"
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:src="@drawable/ic_ferti"
                app:tint="@android:color/holo_green_dark" />

            <TextView
                android:id="@+id/item_text_ferti"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ferti Completa"
                android:textSize="16sp"
                android:layout_marginStart="8dp"
                android:textColor="#444444" />
        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>

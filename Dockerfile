FROM oven/bun:1.2.20 as base

# Set the working directory
WORKDIR /app
COPY . .
RUN bun install

WORKDIR /app/packages/protobuf
# Build the proto package
RUN bun install
RUN bun run build:prod

WORKDIR /app/packages/mqtt-integration
# Build the mqtt-integration package
RUN bun install
RUN bun run build

WORKDIR /app

FROM base AS mqtt-integration
WORKDIR /app/packages/mqtt-integration
CMD [ "bun", "run", "dist/index.js" ]